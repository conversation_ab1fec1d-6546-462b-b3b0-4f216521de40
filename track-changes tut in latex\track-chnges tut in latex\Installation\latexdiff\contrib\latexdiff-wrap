#!/bin/bash
#
# latexdiff-wrap
#
# Wrapper for latexdiff, to
#   * provide support for documents consiting of more than 1 latex file
#   * provide my common arguments
#
# Copyright (C) by <PERSON><PERSON> <<PERSON><PERSON><PERSON><PERSON><EMAIL>>
# Released under the terms of the GNU General Public License (GPL) Version 2.
# See http://www.gnu.org/ for details.
#
# Vol<PERSON>mann
#   5, 6, 7, 12, 16, 17 Oct 2005
#   31 Jan; 5, 7, 13, 15 Feb 2006
#

VERSION="0.6, 15 Feb 2006"
AUTHOR="<PERSON><PERSON> <<PERSON><PERSON><PERSON><EMAIL>>"
COPYRIGHT="Copyright (C) 2005-2006"


####
#### Constants and initialised variables
#
diffcmd="latexdiff"
diffrc="$HOME/texmf/latexdiff"
#diffargs="-e latin1 --ignore-warnings -p latexdiff-preamble.sty"
diffargs="-e latin1 --ignore-warnings"
diffargs="$diffargs --append-safecmd $diffrc/safe-cmds"
diffargs="$diffargs --append-textcmd $diffrc/text-cmds"
# Note: Can't use multiple --append-safecmd
# show current command lists:
#diffcmd="$diffcmd --show-safecmd --show-textcmd --show-config"


####
#### Version, Usage, Help
#
show_version() {
    echo "${0##*/} version $VERSION
$COPYRIGHT by $AUTHOR"
}

show_usage() {
    echo "
Usage: ${0##*/} OLDDIR NEWDIR DIFFDIR [DIFFARGS --] FILE.tex [...]
       ${0##*/} --show [DIFFARGS]
Version $VERSION
$COPYRIGHT by $AUTHOR
"
}

show_help() {
    show_usage
    echo "\
For each FILE.tex, build a new file DIFFDIR/FILE.tex with markup of the changes
which were made from OLDDIR/FILE.tex to NEWDIR/FILE.tex.
Any path given with FILE.tex is stripped off.
Any DIFFARGS are added to the latexdiff call, if present (remember to follow
them with a double-hyphen on its own before the FILE arguments).

With --show, shows the settings latexdiff would be running with, including the
changes applied by the user.
"
}

# For scripts not using function library only:
Version() { show_version; exitwith ErrVersion; }
Usage() { show_help; exitwith ErrUsage; }
Help() { test "$1" && exitwith ErrHelp show_help; show_help; exitwith ErrOK; }


####
#### Error/Exit codes
#
exitwith() {
    exec 1>&2  # write stdout on stderr instead
  case "$1" in
    ErrOK)
    	exit 0;;
    ErrVersion|ErrUsage|ErrHelp)
	# Output generated by function (program) $2, if given
	test -n "$2" && "$2"
	exit 1;;
 # more codes in here
 # more codes in here
    ErrBadoption)
    	echo "Bad option '$2'."
	echo "Call with -h for help."
	exit 9;;
    ErrMissingParameter)
	echo "A required parameter for option $2 is missing."
	echo "Call with -h for help."
	exit 9;;
    *)
	echo "Internal error: exitwith() called with illegal error code '$1'."
	exit 19;;
  esac
}


####
#### Parse command line parameters
#

# If the next arg starts with a "-", collect additional argument for latexdiff
# until "--".
scanextraargs() {
    addargs=()
    case "$1" in -*)
	while [ $# -gt 0 -a "$1" != "--" ]; do
    	    addargs=( "${addargs[@]}" "$1" )
	    shift
	done
	test "$1" == "--" && shift
    ;; esac
    fileargs=( "$@" )
}

case "$1" in
    --version)	    	Version;;
    --usage)	    	Usage;;
    --help|-h|-help)	Help;;
    --show)
        shift
	scanextraargs "$@"
	(set -x
	$diffcmd $diffargs "${addargs[@]}" \
	    --show-safecmd --show-textcmd --show-config 
	) | fmt
    	exit $? ;;
esac

olddir="${1%/}"
newdir="${2%/}"
diffdir="${3%/}"

if ! [ -d "$olddir" -a -d "$newdir" -a -d "$diffdir" ]; then
    Help 1>&2 err
fi

shift 3

scanextraargs "$@"
set -- "${fileargs[@]}"



####
#### Functions
#
#set -x
Log() { echo 1>&2 "+ $@"; "$@"; }


####
#### Main
#

# Create output directory, just in case.
(set -x
mkdir -p "$diffdir"
)
while [ $# -gt 0 ]; do
    file="${1##*/}"
    echo Examining: "$file"
    # No point running latexdiff if both files are identical,
    # but run latexdiff on top-level LaTeX file in any case.
    if cmp --quiet "$olddir/$file" "$newdir/$file" \
	&& ! grep -lq '\\begin.*{document}' "$newdir/$file"; then
	(set -x
	cp -p "$olddir/$file" "$diffdir"
	)
    else
    	# Delete file, to make sure it's not clobbered by redirecting stdout
	# in case it's a symlink to te original.
	test -f "$diffdir/$file" && (set -x
	rm "$diffdir/$file"
	)
	# Run latexdiff if both input files are present.
	run=1
	test -f "$olddir/$file" || { echo 1>&2 "No file: $olddir/$file"; run=; }
	test -f "$newdir/$file" || { echo 1>&2 "No file: $newdir/$file"; run=; }
	test -n "$run" && \
	(set -x
	$diffcmd $diffargs "${addargs[@]}" \
	    "$olddir/$file" "$newdir/$file"  > "$diffdir/$file"
	)
    fi
    shift
done
