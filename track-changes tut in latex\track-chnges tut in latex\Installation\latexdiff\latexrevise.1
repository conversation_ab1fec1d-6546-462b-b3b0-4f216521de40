.\" Automatically generated by Pod::Man 2.27 (Pod::Simple 3.28)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is turned on, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{
.    if \nF \{
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "LATEXREVISE 1"
.TH LATEXREVISE 1 "2015-12-27" "perl v5.18.2" " "
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
latexrevise \- selectively remove markup and text from latexdiff output
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
\&\fBlatexrevise\fR [ \fB\s-1OPTIONS\s0\fR ] [ \fIdiff.tex\fR ] > \fIrevised.tex\fR
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
\&\fIlatexrevise\fR reads a file \f(CW\*(C`diff.tex\*(C'\fR (output of \fIlatexdiff\fR), and remove the markup commands. 
If no filename is given the input is read from standard input. The command can be used
in \fI\s-1ACCEPT\s0\fR, \fI\s-1DECLINE\s0\fR, or \fI\s-1SIMPLIFY\s0\fR mode, or can be used to remove user-defined
latex commands from the input (see \fB\-c\fR, \fB\-e\fR, \fB\-m\fR, and \fB\-n\fR below). 
In \fI\s-1ACCEPT\s0\fR mode, all appended text fragments  (or preamble lines)
are kept, and all discarded text fragments (or preamble lines) are
deleted.  
In \fI\s-1DECLINE\s0\fR mode, all discarded text fragments are kept, and all appended 
text fragments are deleted.  
If you wish to keep some changes, edit the diff.tex file in
advance, and manually remove those tokens  which would otherwise be
deleted.  Note that \fIlatexrevise\fR only pays attention to the \f(CW\*(C`\eDIFaddbegin\*(C'\fR,
\&\f(CW\*(C`\eDIFaddend\*(C'\fR, \f(CW\*(C`\eDIFdelbegin\*(C'\fR, and \f(CW\*(C`\eDIFdelend\*(C'\fR tokens and corresponding \s-1FL\s0
varieties.  All \f(CW\*(C`\eDIFadd\*(C'\fR and \f(CW\*(C`\eDIFdel\*(C'\fR commands (but not their contents) are 
simply deleted.   The commands added by latexdiff to the preamble are also
removed.
In \fI\s-1SIMPLIFY\s0\fR mode, \f(CW\*(C`\eDIFaddbegin, \eDIFaddend, \eDIFdelbegin, \eDIFdelend\*(C'\fR
tokens and their corresponding \f(CW\*(C`FL\*(C'\fR varieties are kept but all other markup (e.g. \f(CW\*(C`DIFadd\*(C'\fR and <\eDIFdel>) is removed.  The result
will not in general be valid latex-code but it will be easier to read and edit in 
preparation for a subsequent run in \fI\s-1ACCEPT\s0\fR or \fI\s-1DECLINE\s0\fR mode.  
In \fI\s-1SIMPLIFY\s0\fR mode the preamble is left unmodified.
.SH "OPTIONS"
.IX Header "OPTIONS"
.IP "\fB\-a\fR or \fB\-\-accept\fR" 4
.IX Item "-a or --accept"
Run in \fI\s-1ACCEPT\s0\fR mode (delete all blocks marked by \f(CW\*(C`\eDIFdelbegin\*(C'\fR and \f(CW\*(C`\eDIFdelend\*(C'\fR).
.IP "\fB\-d\fR or \fB\-\-decline\fR" 4
.IX Item "-d or --decline"
Run in \fI\s-1DECLINE\s0\fR mode (delete all blocks marked by \f(CW\*(C`\eDIFaddbegin\*(C'\fR
and \f(CW\*(C`\eDIFaddend\*(C'\fR).
.IP "\fB\-s\fR or \fB\-\-simplify\fR" 4
.IX Item "-s or --simplify"
Run in \fI\s-1SIMPLIFY\s0\fR mode (Keep all \f(CW\*(C`\eDIFaddbegin\*(C'\fR, \f(CW\*(C`\eDIFaddend\*(C'\fR, 
\&\f(CW\*(C`\eDIFdelbegin\*(C'\fR, \f(CW\*(C`\eDIFdelend\*(C'\fR tokens, but remove all other latexdiff
markup from body).
.PP
Note that the three mode options are mutually exclusive.  If no mode option is given,
\&\fIlatexrevise\fR simply removes user annotations and markup according to the following four
options.
.IP "\fB\-c cmd\fR or \fB\-\-comment=cmd\fR" 4
.IX Item "-c cmd or --comment=cmd"
Remove \f(CW\*(C`\ecmd{...}\*(C'\fR sequences.  \f(CW\*(C`cmd\*(C'\fR is supposed to mark some explicit 
anotations which should be removed from the file before 
release.
.IP "\fB\-e envir\fR or \fB\-\-comment\-environment=envir\fR" 4
.IX Item "-e envir or --comment-environment=envir"
Remove explicit annotation environments from the text, i.e. remove
.Sp
.Vb 3
\&            \ebegin{envir}
\&            ...
\&            \eend{envir}
.Ve
.Sp
blocks.
.IP "\fB\-m cmd\fR or \fB\-\-markup=cmd\fR" 4
.IX Item "-m cmd or --markup=cmd"
Remove the markup command \f(CW\*(C`\ecmd\*(C'\fR but leave its argument, i.e.
turn \f(CW\*(C`\ecmd{abc}\*(C'\fR into \f(CW\*(C`abc\*(C'\fR.
.IP "\fB\-n envir\fR or \fB\-\-markup\-environment=envir\fR" 4
.IX Item "-n envir or --markup-environment=envir"
Similarly, remove \f(CW\*(C`\ebegin{envir}\*(C'\fR and \f(CW\*(C`\eend{envir}\*(C'\fR commands but 
leave content of the environment in the text.
.IP "\fB\-V\fR or \fB\-\-verbose\fR" 4
.IX Item "-V or --verbose"
Verbose output
.IP "\fB\-q\fR or \fB\-\-no\-warnings\fR" 4
.IX Item "-q or --no-warnings"
Do not warn users about \f(CW\*(C`\eDIDadd{..}\*(C'\fR or \f(CW\*(C`\eDIFdel{..}\*(C'\fR statements
which should have been removed already.
.SH "BUGS"
.IX Header "BUGS"
The current version is a beta version which has not yet been
extensively tested, but worked fine locally.  Please submit bug reports using the issue tracker of the github repository page \fIhttps://github.com/ftilmann/latexdiff.git\fR, 
or send them to \fItilmann \*(-- \s-1AT\s0 \*(-- gfz\-potsdam.de\fR..  Include the serial number of \fIlatexrevise\fR
(Option \-\-version).  If you come across latexdiff
output which is not processed correctly by \fIlatexrevise\fR please include the
problem file as well as the old and new files on which it is based,
ideally edited to only contain the offending passage as long as that still
reproduces the problem.
.PP
Note that \fIlatexrevise\fR gets confused by commented \f(CW\*(C`\ebegin{document}\*(C'\fR or 
\&\f(CW\*(C`\eend{document}\*(C'\fR statements
.SH "SEE ALSO"
.IX Header "SEE ALSO"
latexdiff
.SH "PORTABILITY"
.IX Header "PORTABILITY"
\&\fIlatexrevise\fR does not make use of external commands and thus should run
on any platform  supporting \s-1PERL\s0 v5 or higher.
.SH "AUTHOR"
.IX Header "AUTHOR"
Copyright (C) 2004 Frederik Tilmann
.PP
This program is free software; you can redistribute it and/or modify
it under the terms of the \s-1GNU\s0 General Public License Version 3
