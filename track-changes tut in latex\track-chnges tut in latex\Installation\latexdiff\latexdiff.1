.\" Automatically generated by Pod::Man 2.27 (Pod::Simple 3.28)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is turned on, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{
.    if \nF \{
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "LATEXDIFF 1"
.TH LATEXDIFF 1 "2017-06-22" "perl v5.18.2" " "
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
latexdiff \- determine and markup differences between two latex files
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
\&\fBlatexdiff\fR [ \fB\s-1OPTIONS\s0\fR ] \fIold.tex\fR \fInew.tex\fR > \fIdiff.tex\fR
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
Briefly, \fIlatexdiff\fR is a utility program to aid in the management of
revisions of latex documents. It compares two valid latex files, here
called \f(CW\*(C`old.tex\*(C'\fR and \f(CW\*(C`new.tex\*(C'\fR, finds significant differences
between them (i.e., ignoring the number of white spaces and position
of line breaks), and adds special commands to highlight the
differences.  Where visual highlighting is not possible, e.g. for changes
in the formatting, the differences are
nevertheless marked up in the source.
.PP
The program treats the preamble differently from the main document.
Differences between the preambles are found using line-based
differencing (similarly to the Unix diff command, but ignoring white
spaces).  A comment, "\f(CW\*(C`%DIF\ >\*(C'\fR" is appended to each added line, i.e. a 
line present in \f(CW\*(C`new.tex\*(C'\fR but not in \f(CW\*(C`old.tex\*(C'\fR.  Discarded lines 
 are deactivated by prepending "\f(CW\*(C`%DIF\ <\*(C'\fR". Changed blocks are preceded  by
comment lines giving information about line numbers in the original files.  Where there are insignificant
differences, the resulting file \f(CW\*(C`diff.tex\*(C'\fR will be similar to
\&\f(CW\*(C`new.tex\*(C'\fR.  At the end of the preamble, the definitions for \fIlatexdiff\fR markup commands are inserted.
In differencing the main body of the text, \fIlatexdiff\fR attempts to
satisfy the following guidelines (in order of priority):
.IP "1." 3
If both \f(CW\*(C`old.tex\*(C'\fR and \f(CW\*(C`new.tex\*(C'\fR are valid LaTeX, then the resulting
\&\f(CW\*(C`diff.tex\*(C'\fR should also be valid LateX. (\s-1NB\s0 If a few plain TeX commands
are used within \f(CW\*(C`old.tex\*(C'\fR or \f(CW\*(C`new.tex\*(C'\fR then \f(CW\*(C`diff.tex\*(C'\fR is not
guaranteed to work but usually will).
.IP "2." 3
Significant differences are determined on the level of
individual words. All significant differences, including differences
between comments should be clearly marked in the resulting source code
\&\f(CW\*(C`diff.tex\*(C'\fR.
.IP "3." 3
If a changed passage contains text or text-producing commands, then
running \f(CW\*(C`diff.tex\*(C'\fR through LateX should produce output where added
and discarded passages are highlighted.
.IP "4." 3
Where there are insignificant differences, e.g. in the positioning of
line breaks, \f(CW\*(C`diff.tex\*(C'\fR should follow the formatting of \f(CW\*(C`new.tex\*(C'\fR
.PP
For differencing the same algorithm as \fIdiff\fR is used but words
instead of lines are compared.  An attempt is made to recognize
blocks which are completely changed such that they can be marked up as a unit.
Comments are differenced line by line
but the number of spaces within comments is ignored. Commands including
all their arguments are generally compared as one unit, i.e., no mark-up
is inserted into the arguments of commands.  However, for a selected
number of commands (for example, \f(CW\*(C`\ecaption\*(C'\fR and all sectioning
commands) the last argument is known to be text. This text is
split into words and differenced just as ordinary text (use options to
show and change the list of text commands, see below). As the
algorithm has no detailed knowledge of LaTeX, it assumes all pairs of
curly braces immediately following a command (i.e. a sequence of
letters beginning with a backslash) are arguments for that command.
As a restriction to condition 1 above it is thus necessary to surround
all arguments with curly braces, and to not insert
extraneous spaces.  For example, write
.PP
.Vb 1
\&  \esection{\etextem{This is an emphasized section title}}
.Ve
.PP
and not
.PP
.Vb 1
\&  \esection {\etextem{This is an emphasized section title}}
.Ve
.PP
or
.PP
.Vb 1
\&  \esection\etextem{This is an emphasized section title}
.Ve
.PP
even though all varieties are the same to LaTeX (but see
\&\fB\-\-allow\-spaces\fR option which allows the second variety).
.PP
For environments whose content does not conform to standard LaTeX or
where graphical markup does not make sense all markup commands can be
removed by setting the \s-1PICTUREENV\s0 configuration variable, set by
default to \f(CW\*(C`picture\*(C'\fR and \f(CW\*(C`DIFnomarkup\*(C'\fR environments; see \fB\-\-config\fR
option).  The latter environment (\f(CW\*(C`DIFnomarkup\*(C'\fR) can be used to
protect parts of the latex file where the markup results in illegal
markup. You have to surround the offending passage in both the old and
new file by \f(CW\*(C`\ebegin{DIFnomarkup}\*(C'\fR and \f(CW\*(C`\eend{DIFnomarkup}\*(C'\fR. You must
define the environment in the preambles of both old and new
documents. I prefer to define it as a null-environment,
.PP
\&\f(CW\*(C`\enewenvironment{DIFnomarkup}{}{}\*(C'\fR
.PP
but the choice is yours.  Any markup within the environment will be
removed, and generally everything within the environment will just be
taken from the new file.
.PP
It is also possible to difference files which do not have a preamble. 
 In this case, the file is processed in the main document
mode, but the definitions of the markup commands are not inserted.
.PP
All markup commands inserted by \fIlatexdiff\fR begin with "\f(CW\*(C`\eDIF\*(C'\fR".  Added
blocks containing words, commands or comments which are in \f(CW\*(C`new.tex\*(C'\fR
but not in \f(CW\*(C`old.tex\*(C'\fR are marked by \f(CW\*(C`\eDIFaddbegin\*(C'\fR and \f(CW\*(C`\eDIFaddend\*(C'\fR.
Discarded blocks are marked by \f(CW\*(C`\eDIFdelbegin\*(C'\fR and \f(CW\*(C`\eDIFdelend\*(C'\fR.
Within added blocks all text is highlighted with \f(CW\*(C`\eDIFadd\*(C'\fR like this:
\&\f(CW\*(C`\eDIFadd{Added text block}\*(C'\fR
Selected `safe' commands can be contained in these text blocks as well
(use options to show and change the list of safe commands, see below).
All other commands as well as braces \*(L"{\*(R" and \*(L"}\*(R" are never put within
the scope of \f(CW\*(C`\eDIFadd\*(C'\fR.  Added comments are marked by prepending
"\f(CW\*(C`%DIF\ >\ \*(C'\fR".
.PP
Within deleted blocks text is highlighted with \f(CW\*(C`\eDIFdel\*(C'\fR.  Deleted
comments are marked by prepending "\f(CW\*(C`%DIF\ <\ \*(C'\fR\*(L".  Non-safe command
and curly braces within deleted blocks are commented out with 
\&\*(R"\f(CW\*(C`%DIFDELCMD\ <\ \*(C'\fR".
.SH "OPTIONS"
.IX Header "OPTIONS"
.SS "Preamble"
.IX Subsection "Preamble"
The following options determine the visual markup style by adding the appropriate
command definitions to the preamble. See the end of this section for a description of 
available styles.
.IP "\fB\-\-type=markupstyle\fR or \fB\-t markupstyle\fR" 4
.IX Item "--type=markupstyle or -t markupstyle"
Add code to preamble for selected markup style. This option defines
\&\f(CW\*(C`\eDIFadd\*(C'\fR and \f(CW\*(C`\eDIFdel\*(C'\fR commands.
Available styles:
.Sp
\&\f(CW\*(C`UNDERLINE CTRADITIONAL TRADITIONAL CFONT FONTSTRIKE INVISIBLE 
CHANGEBAR CCHANGEBAR CULINECHBAR CFONTCBHBAR BOLD PDFCOMMENT\*(C'\fR
.Sp
[ Default: \f(CW\*(C`UNDERLINE\*(C'\fR ]
.IP "\fB\-\-subtype=markstyle\fR or \fB\-s markstyle\fR" 4
.IX Item "--subtype=markstyle or -s markstyle"
Add code to preamble for selected style for bracketing
commands (e.g. to mark changes in  margin). This option defines
\&\f(CW\*(C`\eDIFaddbegin\*(C'\fR, \f(CW\*(C`\eDIFaddend\*(C'\fR, \f(CW\*(C`\eDIFdelbegin\*(C'\fR and \f(CW\*(C`\eDIFdelend\*(C'\fR commands.
Available styles: \f(CW\*(C`SAFE MARGIN COLOR DVIPSCOL  ZLABEL ONLYCHANGEDPAGE (LABEL)*\*(C'\fR
.Sp
[ Default: \f(CW\*(C`SAFE\*(C'\fR ]
* Subtype \f(CW\*(C`LABEL\*(C'\fR is deprecated
.IP "\fB\-\-floattype=markstyle\fR or \fB\-f markstyle\fR" 4
.IX Item "--floattype=markstyle or -f markstyle"
Add code to preamble for selected style which 
replace standard marking and markup commands within floats
(e.g., marginal remarks cause an error within floats
so marginal marking can be disabled thus). This option defines all 
\&\f(CW\*(C`\eDIF...FL\*(C'\fR commands.
Available styles: \f(CW\*(C`FLOATSAFE TRADITIONALSAFE IDENTICAL\*(C'\fR
.Sp
[ Default: \f(CW\*(C`FLOATSAFE\*(C'\fR ]
.IP "\fB\-\-encoding=enc\fR or \fB\-e enc\fR" 4
.IX Item "--encoding=enc or -e enc"
Specify encoding of old.tex and new.tex. Typical encodings are
\&\f(CW\*(C`ascii\*(C'\fR, \f(CW\*(C`utf8\*(C'\fR, \f(CW\*(C`latin1\*(C'\fR, \f(CW\*(C`latin9\*(C'\fR.  A list of available encodings can be 
obtained by executing
.Sp
\&\f(CW\*(C`perl \-MEncode \-e \*(Aqprint join ("\en",Encode\-\*(C'\fRencodings( \*(L":all\*(R" )) ;' >
.Sp
[Default encoding is utf8 unless the first few lines of the preamble contain
an invocation \f(CW\*(C`\eusepackage[..]{inputenc}\*(C'\fR in which case the 
encoding chosen by this command is asssumed. Note that \s-1ASCII \s0(standard
latex) is a subset of utf8]
.IP "\fB\-\-preamble=file\fR or \fB\-p file\fR" 4
.IX Item "--preamble=file or -p file"
Insert file at end of preamble instead of generating
preamble.  The preamble must define the following commands
\&\f(CW\*(C`\eDIFaddbegin, \eDIFaddend, \eDIFadd{..},
\&\eDIFdelbegin,\eDIFdelend,\eDIFdel{..},\*(C'\fR
and varieties for use within floats
\&\f(CW\*(C`\eDIFaddbeginFL, \eDIFaddendFL, \eDIFaddFL{..},
\&\eDIFdelbeginFL, \eDIFdelendFL, \eDIFdelFL{..}\*(C'\fR
(If this option is set \fB\-t\fR, \fB\-s\fR, and \fB\-f\fR options
are ignored.)
.IP "\fB\-\-packages=pkg1,pkg2,..\fR" 4
.IX Item "--packages=pkg1,pkg2,.."
Tell latexdiff that .tex file is processed with the packages in list
loaded.  This is normally not necessary if the .tex file includes the
preamble, as the preamble is automatically scanned for \f(CW\*(C`\eusepackage\*(C'\fR commands.
Use of the \fB\-\-packages\fR option disables automatic scanning, so if for any
reason package specific parsing needs to be switched off, use \fB\-\-packages=none\fR.
The following packages trigger special behaviour:
.RS 4
.ie n .IP """amsmath""" 8
.el .IP "\f(CWamsmath\fR" 8
.IX Item "amsmath"
Configuration variable \s-1MATHARRREPL\s0 is set to \f(CW\*(C`align*\*(C'\fR (Default: \f(CW\*(C`eqnarray*\*(C'\fR). (Note that many of the 
amsmath array environments are already recognised by default as such)
.ie n .IP """endfloat""" 8
.el .IP "\f(CWendfloat\fR" 8
.IX Item "endfloat"
Ensure that \f(CW\*(C`\ebegin{figure}\*(C'\fR and \f(CW\*(C`\eend{figure}\*(C'\fR always appear by themselves on a line.
.ie n .IP """hyperref""" 8
.el .IP "\f(CWhyperref\fR" 8
.IX Item "hyperref"
Change name of \f(CW\*(C`\eDIFadd\*(C'\fR and \f(CW\*(C`\eDIFdel\*(C'\fR commands to \f(CW\*(C`\eDIFaddtex\*(C'\fR and \f(CW\*(C`\eDIFdeltex\*(C'\fR and 
define new \f(CW\*(C`\eDIFadd\*(C'\fR and \f(CW\*(C`\eDIFdel\*(C'\fR commands, which provide a wrapper for these commands,
using them for the text but not for the link defining command (where any markup would cause
errors).
.ie n .IP """apacite""" 8
.el .IP "\f(CWapacite\fR" 8
.IX Item "apacite"
Redefine the commands recognised as citation commands.
.ie n .IP """siunitx""" 8
.el .IP "\f(CWsiunitx\fR" 8
.IX Item "siunitx"
Treat \f(CW\*(C`\eSI\*(C'\fR as equivalent to citation commands (i.e. protect with \f(CW\*(C`\embox\*(C'\fR if markup style uses ulem package.
.ie n .IP """cleveref""" 8
.el .IP "\f(CWcleveref\fR" 8
.IX Item "cleveref"
Treat \f(CW\*(C`\ecref,\eCref\*(C'\fR, etc as equivalent to citation commands (i.e. protect with \f(CW\*(C`\embox\*(C'\fR if markup style uses ulem package.
.ie n .IP """glossaries""" 8
.el .IP "\f(CWglossaries\fR" 8
.IX Item "glossaries"
Define most of the glossaries commands as safe, protecting them with \embox'es where needed
.ie n .IP """mhchem""" 8
.el .IP "\f(CWmhchem\fR" 8
.IX Item "mhchem"
Treat \f(CW\*(C`\ece\*(C'\fR as a safe command, i.e. it will be highlighted (note that \f(CW\*(C`\ecee\*(C'\fR will not be highlighted in equations as this leads to processing errors)
.ie n .IP """chemformula"" or ""chemmacros""" 8
.el .IP "\f(CWchemformula\fR or \f(CWchemmacros\fR" 8
.IX Item "chemformula or chemmacros"
Treat \f(CW\*(C`\ech\*(C'\fR as a safe command outside equations, i.e. it will be highlighted (note that \f(CW\*(C`\ech\*(C'\fR will not be highlighted in equations as this leads to processing errors)
.RE
.RS 4
.Sp
[ Default: scan the preamble for \f(CW\*(C`\eusepackage\*(C'\fR commands to determine
  loaded packages. ]
.RE
.IP "\fB\-\-show\-preamble\fR" 4
.IX Item "--show-preamble"
Print generated or included preamble commands to stdout.
.SS "Configuration"
.IX Subsection "Configuration"
.ie n .IP "\fB\-\-exclude\-safecmd=exclude\-file\fR or \fB\-A exclude-file\fR or  \fB\-\-exclude\-safecmd=""cmd1,cmd2,...""\fR" 4
.el .IP "\fB\-\-exclude\-safecmd=exclude\-file\fR or \fB\-A exclude-file\fR or  \fB\-\-exclude\-safecmd=``cmd1,cmd2,...''\fR" 4
.IX Item "--exclude-safecmd=exclude-file or -A exclude-file or --exclude-safecmd=cmd1,cmd2,..."
.PD 0
.IP "\fB\-\-replace\-safecmd=replace\-file\fR" 4
.IX Item "--replace-safecmd=replace-file"
.ie n .IP "\fB\-\-append\-safecmd=append\-file\fR or \fB\-a append-file\fR or \fB\-\-append\-safecmd=""cmd1,cmd2,...""\fR" 4
.el .IP "\fB\-\-append\-safecmd=append\-file\fR or \fB\-a append-file\fR or \fB\-\-append\-safecmd=``cmd1,cmd2,...''\fR" 4
.IX Item "--append-safecmd=append-file or -a append-file or --append-safecmd=cmd1,cmd2,..."
.PD
Exclude from, replace or append to the list of regular expressions (RegEx)
matching commands which are safe to use within the 
scope of a \f(CW\*(C`\eDIFadd\*(C'\fR or \f(CW\*(C`\eDIFdel\*(C'\fR command.  The file must contain
one Perl-RegEx per line (Comment lines beginning with # or % are
ignored).  Note that the RegEx needs to match the whole of 
the token, i.e., /^regex$/ is implied and that the initial
\&\*(L"\e\*(R" of the command is not included. 
The \fB\-\-exclude\-safecmd\fR and \fB\-\-append\-safecmd\fR options can be combined with the \-\fB\-\-replace\-safecmd\fR 
option and can be used repeatedly to add cumulatively to the lists.
 \fB\-\-exclude\-safecmd\fR
and \fB\-\-append\-safecmd\fR can also take a comma separated list as input. If a
comma for one of the regex is required, escape it thus \*(L"\e,\*(R". In most cases it
will be necessary to protect the comma-separated list from the shell by putting
it in quotation marks.
.ie n .IP "\fB\-\-exclude\-textcmd=exclude\-file\fR or \fB\-X exclude-file\fR or \fB\-\-exclude\-textcmd=""cmd1,cmd2,...""\fR" 4
.el .IP "\fB\-\-exclude\-textcmd=exclude\-file\fR or \fB\-X exclude-file\fR or \fB\-\-exclude\-textcmd=``cmd1,cmd2,...''\fR" 4
.IX Item "--exclude-textcmd=exclude-file or -X exclude-file or --exclude-textcmd=cmd1,cmd2,..."
.PD 0
.IP "\fB\-\-replace\-textcmd=replace\-file\fR" 4
.IX Item "--replace-textcmd=replace-file"
.ie n .IP "\fB\-\-append\-textcmd=append\-file\fR or \fB\-x append-file\fR or \fB\-\-append\-textcmd=""cmd1,cmd2,...""\fR" 4
.el .IP "\fB\-\-append\-textcmd=append\-file\fR or \fB\-x append-file\fR or \fB\-\-append\-textcmd=``cmd1,cmd2,...''\fR" 4
.IX Item "--append-textcmd=append-file or -x append-file or --append-textcmd=cmd1,cmd2,..."
.PD
Exclude from, replace or append to the list of regular expressions
matching commands whose last argument is text.  See
entry for \fB\-\-exclude\-safecmd\fR directly above for further details.
.IP "\fB\-\-replace\-context1cmd=replace\-file\fR" 4
.IX Item "--replace-context1cmd=replace-file"
.PD 0
.IP "\fB\-\-append\-context1cmd=append\-file\fR or" 4
.IX Item "--append-context1cmd=append-file or"
.ie n .IP "\fB\-\-append\-context1cmd=""cmd1,cmd2,...""\fR" 4
.el .IP "\fB\-\-append\-context1cmd=``cmd1,cmd2,...''\fR" 4
.IX Item "--append-context1cmd=cmd1,cmd2,..."
.PD
Replace or append to the list of regex matching commands
whose last argument is text but which require a particular
context to work, e.g. \f(CW\*(C`\ecaption\*(C'\fR will only work within a figure
or table.  These commands behave like text commands, except when 
they occur in a deleted section, when they are disabled, but their
argument is shown as deleted text.
.IP "\fB\-\-replace\-context2cmd=replace\-file\fR" 4
.IX Item "--replace-context2cmd=replace-file"
.PD 0
.IP "\fB\-\-append\-context2cmd=append\-file\fR or" 4
.IX Item "--append-context2cmd=append-file or"
.ie n .IP "\fB\-\-append\-context2cmd=""cmd1,cmd2,...""\fR" 4
.el .IP "\fB\-\-append\-context2cmd=``cmd1,cmd2,...''\fR" 4
.IX Item "--append-context2cmd=cmd1,cmd2,..."
.PD
As corresponding commands for context1.  The only difference is that
context2 commands are completely disabled in deleted sections, including
their arguments.
.ie n .IP "\fB\-\-exclude\-mboxsafecmd=exclude\-file\fR or \fB\-\-exclude\-mboxsafecmd=""cmd1,cmd2,...""\fR" 4
.el .IP "\fB\-\-exclude\-mboxsafecmd=exclude\-file\fR or \fB\-\-exclude\-mboxsafecmd=``cmd1,cmd2,...''\fR" 4
.IX Item "--exclude-mboxsafecmd=exclude-file or --exclude-mboxsafecmd=cmd1,cmd2,..."
.PD 0
.ie n .IP "\fB\-\-append\-mboxsafecmd=append\-file\fR or \fB\-\-append\-mboxsafecmd=""cmd1,cmd2,...""\fR" 4
.el .IP "\fB\-\-append\-mboxsafecmd=append\-file\fR or \fB\-\-append\-mboxsafecmd=``cmd1,cmd2,...''\fR" 4
.IX Item "--append-mboxsafecmd=append-file or --append-mboxsafecmd=cmd1,cmd2,..."
.PD
Define safe commands, which additionally need to be protected by encapsulating
in an \f(CW\*(C`\embox{..}\*(C'\fR. This is sometimes needed to get around incompatibilities 
between external packages and the ulem package, which is  used for highlighting
in the default style \s-1UNDERLINE\s0 as well as \s-1CULINECHBAR CFONTSTRIKE\s0
.IP "\fB\-\-config var1=val1,var2=val2,...\fR or \fB\-c var1=val1,..\fR" 4
.IX Item "--config var1=val1,var2=val2,... or -c var1=val1,.."
.PD 0
.IP "\fB\-c configfile\fR" 4
.IX Item "-c configfile"
.PD
Set configuration variables.  The option can be repeated to set different
variables (as an alternative to the comma-separated list).
Available variables (see below for further explanations):
.Sp
\&\f(CW\*(C`ARRENV\*(C'\fR (RegEx)
.Sp
\&\f(CW\*(C`COUNTERCMD\*(C'\fR (RegEx)
.Sp
\&\f(CW\*(C`FLOATENV\*(C'\fR (RegEx)
.Sp
\&\f(CW\*(C`ITEMCMD\*(C'\fR (RegEx)
.Sp
\&\f(CW\*(C`LISTENV\*(C'\fR  (RegEx)
.Sp
\&\f(CW\*(C`MATHARRENV\*(C'\fR (RegEx)
.Sp
\&\f(CW\*(C`MATHARRREPL\*(C'\fR (String)
.Sp
\&\f(CW\*(C`MATHENV\*(C'\fR (RegEx)
.Sp
\&\f(CW\*(C`MATHREPL\*(C'\fR (String)
.Sp
\&\f(CW\*(C`MINWORDSBLOCK\*(C'\fR (Integer)
.Sp
\&\f(CW\*(C`PICTUREENV\*(C'\fR (RegEx)
.Sp
\&\f(CW\*(C`SCALEDELGRAPHICS\*(C'\fR (Float)
.IP "\fB\-\-add\-to\-config varenv1=pattern1,varenv2=pattern2,...\fR" 4
.IX Item "--add-to-config varenv1=pattern1,varenv2=pattern2,..."
For configuration variables, which are a regular expression (essentially those ending
in \s-1ENV,\s0 and \s-1COUNTERCMD,\s0 see list above) this provides an alternative way to modify the configuration 
variables. Instead of setting the complete pattern, with this option it is possible to add an
alternative pattern. \f(CW\*(C`varenv\*(C'\fR must be one of the variables listed above that take a regular
expression as argument, and pattern is any regular expression (which might need to be 
protected from the shell by quotation). Several patterns can be added at once by using semi-colons
to separate them, e.g. \f(CW\*(C`\-\-add\-to\-config "LISTENV=myitemize;myenumerate,COUNTERCMD=endnote"\*(C'\fR
.IP "\fB\-\-show\-safecmd\fR" 4
.IX Item "--show-safecmd"
Print list of RegEx matching and excluding safe commands.
.IP "\fB\-\-show\-textcmd\fR" 4
.IX Item "--show-textcmd"
Print list of RegEx matching and excluding commands with text argument.
.IP "\fB\-\-show\-config\fR" 4
.IX Item "--show-config"
Show values of configuration variables.
.IP "\fB\-\-show\-all\fR" 4
.IX Item "--show-all"
Combine all \-\-show commands.
.Sp
\&\s-1NB\s0 For all \-\-show commands, no \f(CW\*(C`old.tex\*(C'\fR or \f(CW\*(C`new.tex\*(C'\fR file needs to be specified, and no 
differencing takes place.
.SS "Other configuration options:"
.IX Subsection "Other configuration options:"
.IP "\fB\-\-allow\-spaces\fR" 4
.IX Item "--allow-spaces"
Allow spaces between bracketed or braced arguments to commands.  Note
that this option might have undesirable side effects (unrelated scope
might get lumpeded with preceding commands) so should only be used if the
default produces erroneous results.  (Default requires arguments to
directly follow each other without intervening spaces).
.IP "\fB\-\-math\-markup=level\fR" 4
.IX Item "--math-markup=level"
Determine granularity of markup in displayed math environments:               
Possible values for level are (both numerical and text labels are acceptable):
.Sp
\&\f(CW\*(C`off\*(C'\fR or \f(CW0\fR: suppress markup for math environments.  Deleted equations will not 
appear in diff file. This mode can be used if all the other modes 
cause invalid latex code.
.Sp
\&\f(CW\*(C`whole\*(C'\fR or \f(CW1\fR: Differencing on the level of whole equations. Even trivial changes
to equations cause the whole equation to be marked changed.  This 
mode can be used if processing in coarse or fine mode results in 
invalid latex code.
.Sp
\&\f(CW\*(C`coarse\*(C'\fR or \f(CW2\fR: Detect changes within equations marked up with a coarse
granularity; changes in equation type (e.g.displaymath to equation) 
appear as a change to the complete equation. This mode is recommended
for situations where the content and order of some equations are still
being changed. [Default]
.Sp
\&\f(CW\*(C`fine\*(C'\fR or \f(CW3\fR: Detect small change in equations and mark up at fine granularity.
This mode is most suitable, if only minor changes to equations are
expected, e.g. correction of typos.
.IP "\fB\-\-graphics\-markup=level\fR" 4
.IX Item "--graphics-markup=level"
.Vb 1
\& Change highlight style for graphics embedded with C<\eincludegraphics> commands.
.Ve
.Sp
Possible values for level:
.Sp
\&\f(CW\*(C`none\*(C'\fR, \f(CW\*(C`off\*(C'\fR or \f(CW0\fR: no highlighting for figures
.Sp
\&\f(CW\*(C`new\-only\*(C'\fR or \f(CW1\fR: surround newly added or changed figures with a blue frame [Default if graphicx package loaded]
.Sp
\&\f(CW\*(C`both\*(C'\fR or \f(CW2\fR:     highlight new figures with a blue frame and show deleted figures at reduced 
scale, and crossed out with a red diagonal cross. Use configuration
variable \s-1SCALEDELGRAPHICS\s0 to set size of deleted figures.
.Sp
Note that changes to the optional parameters will make the figure appear as changed 
to latexdiff, and this figure will thus be highlighted
.IP "\fB\-\-disable\-citation\-markup\fR or \fB\-\-disable\-auto\-mbox\fR" 4
.IX Item "--disable-citation-markup or --disable-auto-mbox"
Suppress citation markup and markup of other vulnerable commands in styles 
using ulem (\s-1UNDERLINE,FONTSTRIKE, CULINECHBAR\s0)
(the two options are identical and are simply aliases)
.IP "\fB\-\-enable\-citation\-markup\fR or \fB\-\-enforce\-auto\-mbox\fR" 4
.IX Item "--enable-citation-markup or --enforce-auto-mbox"
Protect citation commands and other vulnerable commands in changed sections 
with \f(CW\*(C`\embox\*(C'\fR command, i.e. use default behaviour for ulem package for other packages
(the two options are identical and are simply aliases)
.SS "Miscellaneous"
.IX Subsection "Miscellaneous"
.IP "\fB\-\-verbose\fR or \fB\-V\fR" 4
.IX Item "--verbose or -V"
Output various status information to stderr during processing.
Default is to work silently.
.IP "\fB\-\-driver=type\fR" 4
.IX Item "--driver=type"
Choose driver for changebar package (only relevant for styles using
   changebar: \s-1CCHANGEBAR CFONTCHBAR CULINECHBAR CHANGEBAR\s0). Possible
drivers are listed in changebar manual, e.g. pdftex,dvips,dvitops
  [Default: dvips]
.IP "\fB\-\-ignore\-warnings\fR" 4
.IX Item "--ignore-warnings"
Suppress warnings about inconsistencies in length between input and
parsed strings and missing characters.  These warning messages are
often related to non-standard latex or latex constructions with a
syntax unknown to \f(CW\*(C`latexdiff\*(C'\fR but the resulting difference argument
is often fully functional anyway, particularly if the non-standard
latex only occurs in parts of the text which have not changed.
.IP "\fB\-\-label=label\fR or \fB\-L label\fR" 4
.IX Item "--label=label or -L label"
Sets the labels used to describe the old and new files.  The first use
of this option sets the label describing the old file and the second
use of the option sets the label for the new file, i.e. set both
labels like this \f(CW\*(C`\-L labelold \-L labelnew\*(C'\fR.
[Default: use the filename and modification dates for the label]
.IP "\fB\-\-no\-label\fR" 4
.IX Item "--no-label"
Suppress inclusion of old and new file names as comment in output file
.IP "\fB\-\-visible\-label\fR" 4
.IX Item "--visible-label"
Include old and new filenames (or labels set with \f(CW\*(C`\-\-label\*(C'\fR option) as 
visible output.
.IP "\fB\-\-flatten\fR" 4
.IX Item "--flatten"
Replace \f(CW\*(C`\einput\*(C'\fR and \f(CW\*(C`\einclude\*(C'\fR commands within body by the content
of the files in their argument.  If \f(CW\*(C`\eincludeonly\*(C'\fR is present in the
preamble, only those files are expanded into the document. However, 
no recursion is done, i.e. \f(CW\*(C`\einput\*(C'\fR and \f(CW\*(C`\einclude\*(C'\fR commands within 
included sections are not expanded.  The included files are assumed to 
 be located in the same directories as the old and new master files,
respectively, making it possible to organise files into old and new directories. 
\&\-\-flatten is applied recursively, so inputted files can contain further
\&\f(CW\*(C`\einput\*(C'\fR statements.
.Sp
Use of this option might result in prohibitive processing times for
larger documents, and the resulting difference document
no longer reflects the structure of the input documents.
.IP "\fB\-\-help\fR or \fB\-h\fR" 4
.IX Item "--help or -h"
Show help text
.IP "\fB\-\-version\fR" 4
.IX Item "--version"
Show version number
.SS "Predefined styles"
.IX Subsection "Predefined styles"
.SS "Major types"
.IX Subsection "Major types"
The major type determine the markup of plain text and some selected latex commands outside floats by defining the markup commands \f(CW\*(C`\eDIFadd{...}\*(C'\fR and \f(CW\*(C`\eDIFdel{...}\*(C'\fR .
.ie n .IP """UNDERLINE""" 10
.el .IP "\f(CWUNDERLINE\fR" 10
.IX Item "UNDERLINE"
Added text is wavy-underlined and blue, discarded text is struck out and red
(Requires color and ulem packages).  Overstriking does not work in displayed math equations such that deleted parts of equation are underlined, not struck out (this is a shortcoming inherent to the ulem package).
.ie n .IP """CTRADITIONAL""" 10
.el .IP "\f(CWCTRADITIONAL\fR" 10
.IX Item "CTRADITIONAL"
Added text is blue and set in sans-serif, and a red footnote is created for each discarded 
piece of text. (Requires color package)
.ie n .IP """TRADITIONAL""" 10
.el .IP "\f(CWTRADITIONAL\fR" 10
.IX Item "TRADITIONAL"
Like \f(CW\*(C`CTRADITIONAL\*(C'\fR but without the use of color.
.ie n .IP """CFONT""" 10
.el .IP "\f(CWCFONT\fR" 10
.IX Item "CFONT"
Added text is blue and set in sans-serif, and discarded text is red and very small size.
.ie n .IP """FONTSTRIKE""" 10
.el .IP "\f(CWFONTSTRIKE\fR" 10
.IX Item "FONTSTRIKE"
Added tex is set in sans-serif, discarded text small and struck out
.ie n .IP """CCHANGEBAR""" 10
.el .IP "\f(CWCCHANGEBAR\fR" 10
.IX Item "CCHANGEBAR"
Added text is blue, and discarded text is red.  Additionally, the changed text is marked with a bar in the margin (Requires color and changebar packages).
.ie n .IP """CFONTCHBAR""" 10
.el .IP "\f(CWCFONTCHBAR\fR" 10
.IX Item "CFONTCHBAR"
Like \f(CW\*(C`CFONT\*(C'\fR but with additional changebars (Requires color and changebar packages).
.ie n .IP """CULINECHBAR""" 10
.el .IP "\f(CWCULINECHBAR\fR" 10
.IX Item "CULINECHBAR"
Like \f(CW\*(C`UNDERLINE\*(C'\fR but with additional changebars (Requires color, ulem and changebar packages).
.ie n .IP """CHANGEBAR""" 10
.el .IP "\f(CWCHANGEBAR\fR" 10
.IX Item "CHANGEBAR"
No mark up of text, but mark margins with changebars (Requires changebar package).
.ie n .IP """INVISIBLE""" 10
.el .IP "\f(CWINVISIBLE\fR" 10
.IX Item "INVISIBLE"
No visible markup (but generic markup commands will still be inserted.
.ie n .IP """BOLD""" 10
.el .IP "\f(CWBOLD\fR" 10
.IX Item "BOLD"
Added text is set in bold face, discarded is not shown.
.ie n .IP """PDFCOMMENT""" 10
.el .IP "\f(CWPDFCOMMENT\fR" 10
.IX Item "PDFCOMMENT"
The pdfcomment package is used to underline new text, and mark deletions with a \s-1PDF\s0 comment. Note that this markup might appear differently or not at all based on the pdf viewer used. The viewer with best support for pdf markup is probably acroread. This style is only recommended if the number of differences is small.
.SS "Subtypes"
.IX Subsection "Subtypes"
The subtype defines the commands that are inserted at the begin and end of added or discarded blocks, irrespectively of whether these blocks contain text or commands (Defined commands: \f(CW\*(C`\eDIFaddbegin, \eDIFaddend, \eDIFdelbegin, \eDIFdelend\*(C'\fR)
.ie n .IP """SAFE""" 10
.el .IP "\f(CWSAFE\fR" 10
.IX Item "SAFE"
No additional markup (Recommended choice)
.ie n .IP """MARGIN""" 10
.el .IP "\f(CWMARGIN\fR" 10
.IX Item "MARGIN"
Mark beginning and end of changed blocks with symbols in the margin nearby (using
the standard \f(CW\*(C`\emarginpar\*(C'\fR command \- note that this sometimes moves somewhat
from the intended position.
.ie n .IP """COLOR""" 10
.el .IP "\f(CWCOLOR\fR" 10
.IX Item "COLOR"
An alternative way of marking added passages in blue, and deleted ones in red.
(It is recommeneded to use instead the main types to effect colored markup,
although in some cases coloring with dvipscol can be more complete, for example 
with citation commands).
.ie n .IP """DVIPSCOL""" 10
.el .IP "\f(CWDVIPSCOL\fR" 10
.IX Item "DVIPSCOL"
An alternative way of marking added passages in blue, and deleted ones in red. Note
that \f(CW\*(C`DVIPSCOL\*(C'\fR only works with the dvips converter, e.g. not pdflatex.
(it is recommeneded to use instead the main types to effect colored markup,
although in some cases coloring with dvipscol can be more complete).
.ie n .IP """ZLABEL""" 10
.el .IP "\f(CWZLABEL\fR" 10
.IX Item "ZLABEL"
can be used to highlight only changed pages, but requires post-processing. It is recommend to not call this option manually but use \f(CW\*(C`latexdiff\-vc\*(C'\fR with \f(CW\*(C`\-\-only\-changes\*(C'\fR option. Alternatively, use the script given within preamble of diff files made using this style.
.ie n .IP """ONLYCHANGEDPAGE""" 10
.el .IP "\f(CWONLYCHANGEDPAGE\fR" 10
.IX Item "ONLYCHANGEDPAGE"
also highlights changed pages, without the need for post-processing, but might not work reliably if
there is floating material (figures, tables).
.ie n .IP """LABEL""" 10
.el .IP "\f(CWLABEL\fR" 10
.IX Item "LABEL"
is similar to \f(CW\*(C`ZLABEL\*(C'\fR, but does not need the zref package and works less reliably (deprecated).
.SS "Float Types"
.IX Subsection "Float Types"
Some of the markup used in the main text might cause problems when used within 
floats (e.g. figures or tables).  For this reason alternative versions of all
markup commands are used within floats. The float type defines these alternative commands.
.ie n .IP """FLOATSAFE""" 10
.el .IP "\f(CWFLOATSAFE\fR" 10
.IX Item "FLOATSAFE"
Use identical markup for text as in the main body, but set all commands marking the begin and end of changed blocks to null-commands.  You have to choose this float type if your subtype is \f(CW\*(C`MARGIN\*(C'\fR as \f(CW\*(C`\emarginpar\*(C'\fR does not work properly within floats.
.ie n .IP """TRADITIONALSAFE""" 10
.el .IP "\f(CWTRADITIONALSAFE\fR" 10
.IX Item "TRADITIONALSAFE"
Mark additions the same way as in the main text.  Deleted environments are marked by angular brackets \e[ and \e] and the deleted text is set in scriptscript size. This float type should always be used with the \f(CW\*(C`TRADITIONAL\*(C'\fR and  \f(CW\*(C`CTRADITIONAL\*(C'\fR markup types as the \efootnote command does not work properly in floating environments.
.ie n .IP """IDENTICAL""" 10
.el .IP "\f(CWIDENTICAL\fR" 10
.IX Item "IDENTICAL"
Make no difference between the main text and floats.
.SS "Configuration Variables"
.IX Subsection "Configuration Variables"
.ie n .IP """ARRENV""" 10
.el .IP "\f(CWARRENV\fR" 10
.IX Item "ARRENV"
If a match to \f(CW\*(C`ARRENV\*(C'\fR is found within an inline math environment within a deleted or added block, then the inlined math 
is surrounded by \f(CW\*(C`\embox{\*(C'\fR...\f(CW\*(C`}\*(C'\fR.  This is necessary as underlining does not work within inlined array environments.
.Sp
[ Default: \f(CW\*(C`ARRENV\*(C'\fR=\f(CW\*(C`(?:array|[pbvBV]matrix)\*(C'\fR\ 
.ie n .IP """COUNTERCMD""" 10
.el .IP "\f(CWCOUNTERCMD\fR" 10
.IX Item "COUNTERCMD"
If a command in a deleted block which is also in the textcmd list matches \f(CW\*(C`COUNTERCMD\*(C'\fR then an
additional command \f(CW\*(C`\eaddtocounter{\*(C'\fR\fIcntcmd\fR\f(CW\*(C`}{\-1}\*(C'\fR, where \fIcntcmd\fR is the matching command, is appended in the diff file such that the numbering in the diff file remains synchronized with the
numbering in the new file.
.Sp
[ Default: \f(CW\*(C`COUNTERCMD\*(C'\fR=\f(CW\*(C`(?:footnote|part|section|subsection\*(C'\fR ...
.Sp
\&\f(CW\*(C`|subsubsection|paragraph|subparagraph)\*(C'\fR  ]
.ie n .IP """FLOATENV""" 10
.el .IP "\f(CWFLOATENV\fR" 10
.IX Item "FLOATENV"
Environments whose name matches the regular expression in \f(CW\*(C`FLOATENV\*(C'\fR are 
considered floats.  Within these environments, the \fIlatexdiff\fR markup commands
are replaced by their \s-1FL\s0 variaties.
.Sp
[ Default: \f(CW\*(C`(?:figure|table|plate)[\ew\ed*@]*\*(C'\fR\ ]
.ie n .IP """ITEMCMD""" 10
.el .IP "\f(CWITEMCMD\fR" 10
.IX Item "ITEMCMD"
Commands representing new item line with list environments.
.Sp
[ Default: \e\f(CW\*(C`item\*(C'\fR ]
.ie n .IP """LISTENV""" 10
.el .IP "\f(CWLISTENV\fR" 10
.IX Item "LISTENV"
Environments whose name matches the regular expression in \f(CW\*(C`LISTENV\*(C'\fR are list environments.
.Sp
[ Default: \f(CW\*(C`(?:itemize|enumerate|description)\*(C'\fR\ ]
.ie n .IP """MATHENV"",""MATHREPL""" 10
.el .IP "\f(CWMATHENV\fR,\f(CWMATHREPL\fR" 10
.IX Item "MATHENV,MATHREPL"
If both \ebegin and \eend for a math environment (environment name matching \f(CW\*(C`MATHENV\*(C'\fR or \e[ and \e])
are within the same deleted block, they are replaced by a \ebegin and \eend commands for \f(CW\*(C`MATHREPL\*(C'\fR
rather than being commented out.
.Sp
[ Default: \f(CW\*(C`MATHENV\*(C'\fR=\f(CW\*(C`(?:displaymath|equation)\*(C'\fR\ , \f(CW\*(C`MATHREPL\*(C'\fR=\f(CW\*(C`displaymath\*(C'\fR\ ]
.ie n .IP """MATHARRENV"",""MATHARRREPL""" 10
.el .IP "\f(CWMATHARRENV\fR,\f(CWMATHARRREPL\fR" 10
.IX Item "MATHARRENV,MATHARRREPL"
as \f(CW\*(C`MATHENV\*(C'\fR,\f(CW\*(C`MATHREPL\*(C'\fR but for equation arrays
.Sp
[ Default: \f(CW\*(C`MATHARRENV\*(C'\fR=\f(CW\*(C`eqnarray\e*?\*(C'\fR\ , \f(CW\*(C`MATHREPL\*(C'\fR=\f(CW\*(C`eqnarray\*(C'\fR\ ]
.ie n .IP """MINWORDSBLOCK""" 10
.el .IP "\f(CWMINWORDSBLOCK\fR" 10
.IX Item "MINWORDSBLOCK"
Minimum number of tokens required to form an independent block. This value is
used in the algorithm to detect changes of complete blocks by merging identical text parts of less than \f(CW\*(C`MINWORDSBLOCK\*(C'\fR to the preceding added and discarded parts.
.Sp
[ Default: 3 ]
.ie n .IP """PICTUREENV""" 10
.el .IP "\f(CWPICTUREENV\fR" 10
.IX Item "PICTUREENV"
Within environments whose name matches the regular expression in \f(CW\*(C`PICTUREENV\*(C'\fR
all latexdiff markup is removed (in pathologic cases this might lead to
inconsistent markup but this situation should be rare).
.Sp
[ Default: \f(CW\*(C`(?:picture|DIFnomarkup)[\ew\ed*@]*\*(C'\fR\ ]
.ie n .IP """SCALEDELGRAPHICS""" 10
.el .IP "\f(CWSCALEDELGRAPHICS\fR" 10
.IX Item "SCALEDELGRAPHICS"
If \f(CW\*(C`\-\-graphics\-markup=both\*(C'\fR is chosen, \f(CW\*(C`SCALEDELGRAPHICS\*(C'\fR is the factor, by which deleted figures will be scaled (i.e. 0.5 implies they are shown at half linear size).
.Sp
[ Default: 0.5 ]
.SH "COMMON PROBLEMS AND FAQ"
.IX Header "COMMON PROBLEMS AND FAQ"
.IP "Citations result in overfull boxes" 10
.IX Item "Citations result in overfull boxes"
There is an incompatibility between the \f(CW\*(C`ulem\*(C'\fR package, which \f(CW\*(C`latexdiff\*(C'\fR uses for underlining and striking out in the \s-1UNDERLINE\s0 style,
the default style, and the way citations are generated. In order to be able to mark up citations properly, they are enclosed with an \f(CW\*(C`\embox\*(C'\fR 
command. As mboxes cannot be broken across lines, this procedure frequently results in overfull boxes, possibly obscuring the content as it extends beyond the right margin.  The same occurs for some other packages (e.g., siunitx). If this is a problem, you have two possibilities.
.Sp
1. Use \f(CW\*(C`CFONT\*(C'\fR type markup (option \f(CW\*(C`\-t CFONT\*(C'\fR): If this markup is chosen, then changed citations are no longer marked up
with the wavy line (additions) or struck out (deletions), but are still highlighted in the appropriate color, and deleted text is shown with a different font. Other styles not using the \f(CW\*(C`ulem\*(C'\fR package will also work.
.Sp
2. Choose option \f(CW\*(C`\-\-disable\-citation\-markup\*(C'\fR which turns off the marking up of citations: deleted citations are no longer shown, and
added citations are shown without markup. (This was the default behaviour of latexdiff at versions 0.6 and older)
.Sp
For custom packages you can define the commands which need to be protected by \f(CW\*(C`\embox\*(C'\fR with \f(CW\*(C`\-\-append\-mboxsafecmd\*(C'\fR and \f(CW\*(C`\-\-excludemboxsafecmd\*(C'\fR options
(submit your lists of command as feature request at github page to set the default behaviour of future versions, see section 6)
.IP "Changes in complicated mathematical equations result in latex processing errors" 10
.IX Item "Changes in complicated mathematical equations result in latex processing errors"
Try options \f(CW\*(C`\-\-math\-markup=whole\*(C'\fR.   If even that fails, you can turn off mark up for equations with \f(CW\*(C`\-\-math\-markup=off\*(C'\fR.
.IP "How can I just show the pages where changes had been made" 10
.IX Item "How can I just show the pages where changes had been made"
Use options \-\f(CW\*(C`\-s ZLABEL\*(C'\fR  (some postprocessing required) or \f(CW\*(C`\-s ONLYCHANGEDPAGE\*(C'\fR. \f(CW\*(C`latexdiff\-vc \-\-ps|\-\-pdf\*(C'\fR with \f(CW\*(C`\-\-only\-changes\*(C'\fR option takes care of
the post-processing for you (requires zref package to be installed).
.SH "BUGS"
.IX Header "BUGS"
.IP "Option allow-spaces not implemented entirely consistently. It breaks the rules that number and type of white space does not matter, as different numbers of inter-argument spaces are treated as significant." 10
.IX Item "Option allow-spaces not implemented entirely consistently. It breaks the rules that number and type of white space does not matter, as different numbers of inter-argument spaces are treated as significant."
.PP
Please submit bug reports using the issue tracker of the github repository page \fIhttps://github.com/ftilmann/latexdiff.git\fR, 
or send them to \fItilmann \*(-- \s-1AT\s0 \*(-- gfz\-potsdam.de\fR.  Include the version number of \fIlatexdiff\fR
(from comments at the top of the source or use \fB\-\-version\fR).  If you come across latex
files that are error-free and conform to the specifications set out
above, and whose differencing still does not result in error-free
latex, please send me those files, ideally edited to only contain the
offending passage as long as that still reproduces the problem. If your 
file relies on non-standard class files, you must include those.  I will not
look at examples where I have trouble to latex the original files.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
latexrevise, latexdiff-vc
.SH "PORTABILITY"
.IX Header "PORTABILITY"
\&\fIlatexdiff\fR does not make use of external commands and thus should run
on any platform  supporting Perl 5.6 or higher.  If files with encodings 
other than \s-1ASCII\s0 or \s-1UTF\-8\s0 are processed, Perl 5.8 or higher is required.
.PP
The standard version of \fIlatexdiff\fR requires installation of the Perl package
\&\f(CW\*(C`Algorithm::Diff\*(C'\fR (available from \fIwww.cpan.org\fR \- 
\&\fIhttp://search.cpan.org/~nedkonz/Algorithm\-Diff\-1.15\fR) but a stand-alone
version, \fIlatexdiff-so\fR, which has this package inlined, is available, too.
\&\fIlatexdiff-fast\fR requires the \fIdiff\fR command to be present.
.SH "AUTHOR"
.IX Header "AUTHOR"
Version 1.2.1
Copyright (C) 2004\-2017 Frederik Tilmann
.PP
This program is free software; you can redistribute it and/or modify
it under the terms of the \s-1GNU\s0 General Public License Version 3
.PP
Contributors of fixes and additions: V. Kuhlmann, J. Paisley, N. Becker, T. Doerges, K. Huebner, 
T. Connors, Sebastian Gouezel and many others.
Thanks to the many people who sent in bug reports, feature suggestions, and other feedback.
