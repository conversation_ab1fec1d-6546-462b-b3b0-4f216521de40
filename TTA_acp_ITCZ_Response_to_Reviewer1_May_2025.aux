\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\citation{alexander2010,wheeler1999}
\citation{alexander2008}
\citation{naujokat1986}
\newlabel{eqn:eqn6}{{1}{3}{2. Methodological Issues and Ambiguities}{equation.0.1}{}}
\bibstyle{plainnat}
\bibdata{Ref_ITCZ.bib}
\bibcite{alexander2008}{{1}{2008}{{<PERSON> et~al.}}{{<PERSON>, <PERSON>~la Torre, and Llamedo}}}
\bibcite{alexander2010}{{2}{2010}{{<PERSON> et~al.}}{{Alexander, Luna, Llamedo, and de~la Torre}}}
\bibcite{naujokat1986}{{3}{1986}{{Naujokat}}{{}}}
\bibcite{wheeler1999}{{4}{1999}{{<PERSON> and <PERSON><PERSON><PERSON>}}{{}}}
\gdef \@abspage@last{5}
