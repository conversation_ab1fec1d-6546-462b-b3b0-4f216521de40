This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: TTA_ACP_ITCZ_June_2025_MD.aux
Reallocating 'name_of_file' (item size: 1) to 11 items.
The style file: copernicus.bst
Reallocating 'name_of_file' (item size: 1) to 9 items.
Database file #1: Ref_ITCZ.bib
I was expecting a `,' or a `}'---line 169 of file Ref_ITCZ.bib
 : 
 : @incollection{reichler2016,
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 231 of file Ref_ITCZ.bib
 : 
 : doi = {10.1175/1520-0442(1993)006<2162:ASDCOT>2.0.CO;2},
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 289 of file Ref_ITCZ.bib
 : 
 : doi={10.1175/2007JAS2518.1}
(<PERSON>rro<PERSON> may have been on previous line)
I'm skipping whatever remains of this entry
Repeated entry---line 1363 of file Ref_ITCZ.bib
 : @article{byrne2018
 :                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 1381 of file Ref_ITCZ.bib
 : @article{gu2002
 :                ,
I'm skipping whatever remains of this entry
Repeated entry---line 2031 of file Ref_ITCZ.bib
 : @article{wei2024
 :                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 2343 of file Ref_ITCZ.bib
 : @article{ern2014
 :                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 2591 of file Ref_ITCZ.bib
 : @article{kerns2018
 :                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 3063 of file Ref_ITCZ.bib
 : @article{fritts2003
 :                    ,
I'm skipping whatever remains of this entry
You're missing a field name---line 3185 of file Ref_ITCZ.bib
 : year = {2008}, 
 :                % Although cited as 2011, BAMS has this as 2008
I'm skipping whatever remains of this entry
You're missing a field name---line 3541 of file Ref_ITCZ.bib
 : year = {2010}, 
 :                % Note: Cited as 2009, but QJ paper is 2010
I'm skipping whatever remains of this entry
Repeated entry---line 3549 of file Ref_ITCZ.bib
 : @article{schmidt2016
 :                     ,
I'm skipping whatever remains of this entry
You've used 70 entries,
            2932 wiz_defined-function locations,
            1493 strings with 25165 characters,
and the built_in function-call counts, 81570 in all, are:
= -- 9462
> -- 3172
< -- 20
+ -- 1481
- -- 991
* -- 5103
:= -- 7871
add.period$ -- 70
call.type$ -- 70
change.case$ -- 471
chr.to.int$ -- 71
cite$ -- 210
duplicate$ -- 8178
empty$ -- 3901
format.name$ -- 1230
if$ -- 16095
int.to.chr$ -- 1
int.to.str$ -- 1
missing$ -- 814
newline$ -- 219
num.names$ -- 280
pop$ -- 3342
preamble$ -- 1
purify$ -- 470
quote$ -- 0
skip$ -- 5181
stack$ -- 0
substring$ -- 3672
swap$ -- 7332
text.length$ -- 3
text.prefix$ -- 0
top$ -- 0
type$ -- 627
warning$ -- 0
while$ -- 384
width$ -- 0
write$ -- 847
(There were 12 error messages)
