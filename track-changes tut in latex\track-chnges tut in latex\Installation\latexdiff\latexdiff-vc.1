.\" Automatically generated by Pod::Man 2.27 (Pod::Simple 3.28)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is turned on, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{
.    if \nF \{
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "LATEXDIFF-VC 1"
.TH LATEXDIFF-VC 1 "2017-06-22" "perl v5.18.2" " "
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
latexdiff\-vc \- wrapper script that calls latexdiff for different versions of a file under version management (CVS, RCS or SVN)
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
\&\fBlatexdiff-vc\fR [ \fIlatexdiff-options\fR ] [ \fIlatexdiff-vc-options\fR ]  \fB\-r\fR [\fIrev1\fR] [\fB\-r\fR \fIrev2\fR]  \fIfile1.tex\fR [ \fIfile2.tex\fR ...]
.PP
.Vb 1
\& or
.Ve
.PP
\&\fBlatexdiff-vc\fR [ \fIlatexdiff-options\fR ]  [ \fIlatexdiff-vc-options\fR ][ \fB\-\-postscript\fR | \fB\-\-pdf\fR ]  \fIold.tex\fR \fInew.tex\fR
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
\&\fIlatexdiff-vc\fR is a wrapper script that applies \fIlatexdiff\fR to a
file, or multiple files under version control (git, subversion (\s-1SVN\s0), mercurial (hg), \s-1CVS, RCS\s0), and optionally runs the
sequence of \f(CW\*(C`latex\*(C'\fR and \f(CW\*(C`dvips\*(C'\fR or \f(CW\*(C`pdflatex\*(C'\fR commands necessary to
produce pdf or postscript output of the difference tex file(s). It can
also be applied to a pair of files to automatise the generation of difference
file in postscript or pdf format.
.SH "OPTIONS"
.IX Header "OPTIONS"
.IP "\fB\-\-rcs\fR, \fB\-\-svn\fR, \fB\-\-cvs\fR, \fB\-\-git\fR or \fB\-\-hg\fR" 4
.IX Item "--rcs, --svn, --cvs, --git or --hg"
Set the version system. 
If no version system is specified, latexdiff-vc will venture a guess.
.Sp
latexdiff-cvs, latexdiff-rcs etc are variants of latexdiff-vc which default to 
the respective versioning system. However, this default can still be overridden using the options above.
.IP "\fB\-r\fR, \fB\-r\fR \fIrev\fR or \fB\-\-revision\fR, \fB\-\-revision=\fR\fIrev\fR" 4
.IX Item "-r, -r rev or --revision, --revision=rev"
Choose revision (under \s-1RCS, CVS, SVN, GIT\s0 or \s-1HG\s0). One or two \fB\-r\fR options can be
specified, and they result in different behaviour:
.RS 4
.IP "\fBlatexdiff-vc\fR \-r \fIfile.tex\fR ..." 4
.IX Item "latexdiff-vc -r file.tex ..."
compares \fIfile.tex\fR with the most recent version checked into \s-1RCS.\s0
.IP "\fBlatexdiff-vc\fR \-r \fIrev1\fR \fIfile.tex\fR ..." 4
.IX Item "latexdiff-vc -r rev1 file.tex ..."
compares \fIfile.tex\fR with revision \fIrev1\fR.
.IP "\fBlatexdiff-vc\fR \-r \fIrev1\fR \-r \fIrev2\fR \fIfile.tex\fR ..." 4
.IX Item "latexdiff-vc -r rev1 -r rev2 file.tex ..."
compares revisions \fIrev1\fR and \fIrev2\fR of \fIfile.tex\fR.
.Sp
Multiple files can be specified for all of the above options. All files must have the
extension \f(CW\*(C`.tex\*(C'\fR, though.
.IP "\fBlatexdiff-vc\fR  \fIold.tex\fR \fInew.tex\fR" 4
.IX Item "latexdiff-vc old.tex new.tex"
compares two files.
.RE
.RS 4
.Sp
The name of the difference file is generated automatically and
reported to stdout.
.RE
.IP "\fB\-d\fR or \fB\-\-dir\fR  \fB\-d\fR \fIpath\fR or \fB\-\-dir=\fR\fIpath\fR" 4
.IX Item "-d or --dir -d path or --dir=path"
Rather than appending the string \f(CW\*(C`diff\*(C'\fR and optionally the version
numbers given to the output-file, this will prepend a directory name \f(CW\*(C`diff\*(C'\fR 
to the
original filename, creating the directory and subdirectories should they not exist already.  This is particularly useful in order to clone a
complete directory hierarchy.  Optionally, a pathname \fIpath\fR can be specified, which is prepended instead of \f(CW\*(C`diff\*(C'\fR.
.IP "\fB\-\-flatten,\-\-flatten=keep\-intermediate\fR" 4
.IX Item "--flatten,--flatten=keep-intermediate"
If combined with \f(CW\*(C`\-\-git\*(C'\fR, \f(CW\*(C`\-\-svn\*(C'\fR or \f(CW\*(C`\-\-hg\*(C'\fR option or the corresponding modes, check out the revisions to compare in a separate temporary directory, and then pass on option \f(CW\*(C`\-\-flatten\*(C'\fR to latexdiff. The directory in which \f(CW\*(C`latexdiff\-vc\*(C'\fR is invoked defines the subtree which will be checked out.
Note that if additional files are needed which are not included in the flatten procedure (package files, included graphics), they need to be accessible in the current directory. If you use bibtex, it is recommended to include the \f(CW\*(C`.bbl\*(C'\fR file in the version management.
.Sp
The generic usage of this function is : \f(CW\*(C`latexdiff\-vc \-\-flatten \-r rev1 [\-r rev2] master.tex\*(C'\fR where master.tex is the project file containing the highest level of includes etc.
.Sp
With \f(CW\*(C`\-\-flatten=keep\-intermediate\*(C'\fR, the intermediate revision snapshots are kept in the current directory (Default is to store them in a temporary directory and delete them after generating the diff file.)
.IP "\fB\-\-config var1=val1,var2=val2,...\fR or \fB\-c var1=val1,..\fR" 4
.IX Item "--config var1=val1,var2=val2,... or -c var1=val1,.."
.PD 0
.IP "\fB\-\-only\-changes\fR" 4
.IX Item "--only-changes"
.PD
Post-process the output such that only pages with changes on them are displayed. This requires the use of subtype \s-1ZLABEL \s0
in latexdiff, which will be set automatically, but any manually set \-s option will be overruled (also requires zref package to 
be installed). (note that this option must be combined with \-\-ps or \-\-pdf to make sense)
.IP "\fB\-\-force\fR" 4
.IX Item "--force"
Overwrite existing diff files without asking for confirmation. Default 
behaviour is to ask for confirmation before overwriting an existing difference
file.
.IP "\fB\-\-run\fR" 4
.IX Item "--run"
run latex command on diff file after generation of diff file.
.IP "\fB\-\-dvi\fR" 4
.IX Item "--dvi"
run latex and dvixxx commands after generation of diff file.
.IP "\fB\-c configfile\fR" 4
.IX Item "-c configfile"
Set configuration variables for latexdiff and latexdiff-vc.  The option can be repeated to set different
variables (as an alternative to the comma-separated list).
Available variables for latexdiff-vc:
.RS 4
.ie n .IP """LATEXDIFF"" latexdiff command (e.g. latexdiff-fast, latexdiff-so). This command should support the option ""\-\-interaction=batchmode""" 8
.el .IP "\f(CWLATEXDIFF\fR latexdiff command (e.g. latexdiff-fast, latexdiff-so). This command should support the option \f(CW\-\-interaction=batchmode\fR" 8
.IX Item "LATEXDIFF latexdiff command (e.g. latexdiff-fast, latexdiff-so). This command should support the option --interaction=batchmode"
.PD 0
.ie n .IP """LATEX"" latex command (e.g. pdflatex, lualatex)" 8
.el .IP "\f(CWLATEX\fR latex command (e.g. pdflatex, lualatex)" 8
.IX Item "LATEX latex command (e.g. pdflatex, lualatex)"
.ie n .IP """DVI2""  Command for conversion of dvi file (e.g. dvips, dvipdf)" 8
.el .IP "\f(CWDVI2\fR  Command for conversion of dvi file (e.g. dvips, dvipdf)" 8
.IX Item "DVI2 Command for conversion of dvi file (e.g. dvips, dvipdf)"
.ie n .IP """BIBTEX"" Command replacing bibtex" 8
.el .IP "\f(CWBIBTEX\fR Command replacing bibtex" 8
.IX Item "BIBTEX Command replacing bibtex"
.RE
.RS 4
.PD
.Sp
All other config variables are passed to latexdiff. Explicity set configuration changes always override implicit
changes by the following shortcut options \fB\-\-fast\fR, \fB\-\-so\fR, \fB\-\-ps\fR and \fB\-\-pdf\fR.
.RE
.IP "\fB\-\-fast\fR or \fB\-\-so\fR" 4
.IX Item "--fast or --so"
Use \f(CW\*(C`latexdiff\-fast\*(C'\fR or \f(CW\*(C`latexdiff\-so\*(C'\fR, respectively (instead of \f(CW\*(C`latexdiff\*(C'\fR).
.IP "\fB\-\-ps\fR or \fB\-\-postscript\fR" 4
.IX Item "--ps or --postscript"
Generate postscript output from difference file.  This will run the
sequence \f(CW\*(C`latex; latex; dvips\*(C'\fR on the difference file (do not use
this option in the rare cases, where three \f(CW\*(C`latex\*(C'\fR commands are
required if you care about correct referencing).  If the difference
file contains a \f(CW\*(C`\ebibliography\*(C'\fR tag, run the sequence \f(CW\*(C`latex;
bibtex; latex; latex; dvips\*(C'\fR.
.IP "\fB\-\-pdf\fR" 4
.IX Item "--pdf"
Generate pdf output from difference file using \f(CW\*(C`pdflatex\*(C'\fR. This will
run the sequence \f(CW\*(C`pdflatex; pdflatex\*(C'\fR on the difference file, or
\&\f(CW\*(C`pdflatex; bibtex; pdflatex; pdflatex\*(C'\fR for files requiring bibtex.
Note that this is not just a shortcut for setting configuration variable but also triggers 
some special behaviour.
.IP "\fB\-\-show\-config\fR" 4
.IX Item "--show-config"
Show values of configuration variables.
.IP "\fB\-\-help\fR or \fB\-h\fR" 4
.IX Item "--help or -h"
Show help text
.IP "\fB\-\-version\fR" 4
.IX Item "--version"
Show version number
.PP
All other options are passed on to \f(CW\*(C`latexdiff\*(C'\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
latexdiff
.SH "PORTABILITY"
.IX Header "PORTABILITY"
\&\fIlatexdiff-vc\fR uses external commands and is therefore dependent on the system architecture; it has been
tested mainly on Unix-like systems. It also requires the a version control
system and latex to be installed on the system to make use of all features.  Modules from Perl 5.8
or higher are required.
.SH "BUG REPORTING"
.IX Header "BUG REPORTING"
Please submit bug reports using the issue tracker of the github repository page \fIhttps://github.com/ftilmann/latexdiff.git\fR, 
or send them to \fItilmann \*(-- \s-1AT\s0 \*(-- gfz\-potsdam.de\fR.  Include the version number of \fIlatexdiff-vc\fR
(option \f(CW\*(C`\-\-version\*(C'\fR).
.SH "AUTHOR"
.IX Header "AUTHOR"
Version 1.2.1
Copyright (C) 2005\-2017 Frederik Tilmann
.PP
This program is free software; you can redistribute it and/or modify
it under the terms of the \s-1GNU\s0 General Public License Version 3
Contributors: S Utcke, H Bruyninckx; some ideas have been inspired by git-latexdiff bash script.
C. Junghans: Mercurial Support.
