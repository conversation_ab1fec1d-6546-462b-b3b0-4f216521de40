This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: TTA_acp_ITCZ_Response_to_Reviewer2_May_2025.aux
Reallocating 'name_of_file' (item size: 1) to 9 items.
The style file: plainnat.bst
Reallocating 'name_of_file' (item size: 1) to 13 items.
Database file #1: Ref_ITCZ.bib
I was expecting a `,' or a `}'---line 169 of file Ref_ITCZ.bib
 : 
 : @incollection{reichler2016,
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 231 of file Ref_ITCZ.bib
 : 
 : doi = {10.1175/1520-0442(1993)006<2162:ASDCOT>2.0.CO;2},
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 289 of file Ref_ITCZ.bib
 : 
 : doi={10.1175/2007JAS2518.1}
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
You're missing a field name---line 3185 of file Ref_ITCZ.bib
 : year = {2008}, 
 :                % Although cited as 2011, BAMS has this as 2008
I'm skipping whatever remains of this entry
You're missing a field name---line 3541 of file Ref_ITCZ.bib
 : year = {2010}, 
 :                % Note: Cited as 2009, but QJ paper is 2010
I'm skipping whatever remains of this entry
You've used 6 entries,
            2773 wiz_defined-function locations,
            673 strings with 6805 characters,
and the built_in function-call counts, 3281 in all, are:
= -- 306
> -- 198
< -- 2
+ -- 66
- -- 60
* -- 304
:= -- 512
add.period$ -- 24
call.type$ -- 6
change.case$ -- 38
chr.to.int$ -- 6
cite$ -- 12
duplicate$ -- 124
empty$ -- 247
format.name$ -- 70
if$ -- 669
int.to.chr$ -- 1
int.to.str$ -- 1
missing$ -- 6
newline$ -- 44
num.names$ -- 24
pop$ -- 58
preamble$ -- 1
purify$ -- 32
quote$ -- 0
skip$ -- 96
stack$ -- 0
substring$ -- 174
swap$ -- 7
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 66
warning$ -- 0
while$ -- 30
width$ -- 0
write$ -- 97
(There were 5 error messages)
