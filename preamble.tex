% preamble.tex

\documentclass[11pt, a4paper]{article}

% --- ENCODING AND FONT ---
\usepackage[utf8]{inputenc} % Standard input encoding
\usepackage[T1]{fontenc}    % Output font encoding for better hyphenation and character rendering
% \usepackage{lmodern}      % Latin Modern font (often a good default)
\usepackage{times}          % Using Times font, common for articles (optional)

% --- PAGE LAYOUT ---
\usepackage[margin=1in]{geometry} % Sets 1-inch margins on all sides (common)
% \usepackage{parskip} % Uncomment for space between paragraphs instead of indentation

% --- MATHEMATICS ---
\usepackage{amsmath}        % For advanced math environments like 'aligned'
\usepackage{amssymb}        % For additional math symbols
\usepackage{amsfonts}       % For math fonts

% --- CITATIONS AND BIBLIOGRAPHY ---
\usepackage[round, sort&compress]{natbib} % For \citep and bibliography management
% 'round' for parentheses, 'sort&compress' for multiple citations

% --- COLORS ---
\usepackage{xcolor}
% Define a color for the responses, e.g., a nice blue
\definecolor{myresponsecolor}{rgb}{0.1, 0.1, 0.7} % A dark blue

% --- CUSTOM ENVIRONMENTS ---
% Environment for colored response text
\newenvironment{responsecolor}{\color{myresponsecolor}}{}

% --- HYPERLINKS AND URLS ---
\usepackage{hyperref}
\hypersetup{
	colorlinks=true,       % false: boxed links; true: colored links
	linkcolor=blue,        % color of internal links (e.g., sections)
	citecolor=blue,        % color of links to bibliography
	filecolor=magenta,     % color of file links
	urlcolor=cyan          % color of external links
}
\usepackage{url}            % For \url command, though hyperref often handles this

% --- GRAPHICS ---
\usepackage{graphicx}       % For including images (though not explicitly used in the snippet, highly likely for a full paper)

% --- LISTS ---
\usepackage{enumitem}       % For more control over list environments (e.g., itemize, enumerate)

% --- UNITS ---
% \usepackage{siunitx}      % For consistent unit formatting, e.g., \SI{30}{\hecto\pascal}
% If you use this, you might define \hPa as \newcommand{\hPa}{\si{\hecto\pascal}}

% --- MISCELLANEOUS ---
% \usepackage{microtype} % For subtle typographic improvements (optional)
% \usepackage{booktabs}  % For professional-looking tables (if you have tables)

% --- METADATA (though already in main.tex, hyperref can use these if defined earlier) ---
% These are usually set in the main document, but good to be aware hyperref might try to pick them up
% \title{Response to Reviewer 1 Comments}
% \author{Toyese Tunde Ayorinde et al.}
% \date{\today}

% --- Line Numbering (useful for review process) ---
% \usepackage{lineno}
% \linenumbers % Turn on line numbering