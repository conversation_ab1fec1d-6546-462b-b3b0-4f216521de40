This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: TTA_acp_ITCZ_Aug_2025_EGU_rev.aux
Reallocating 'name_of_file' (item size: 1) to 11 items.
The style file: copernicus.bst
Reallocating 'name_of_file' (item size: 1) to 13 items.
Database file #1: Ref_ITCZ.bib
I was expecting a `,' or a `}'---line 169 of file Ref_ITCZ.bib
 : 
 : @incollection{reichler2016,
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 231 of file Ref_ITCZ.bib
 : 
 : doi = {10.1175/1520-0442(1993)006<2162:ASDCOT>2.0.CO;2},
(Error may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 289 of file Ref_ITCZ.bib
 : 
 : doi={10.1175/2007JAS2518.1}
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
Repeated entry---line 1363 of file Ref_ITCZ.bib
 : @article{byrne2018
 :                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 2343 of file Ref_ITCZ.bib
 : @article{ern2014
 :                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 2591 of file Ref_ITCZ.bib
 : @article{kerns2018
 :                   ,
I'm skipping whatever remains of this entry
Warning--I didn't find a database entry for "kim2003"
Warning--I didn't find a database entry for "holton2004"
Warning--I didn't find a database entry for "baldwin2001"
Warning--I didn't find a database entry for "schreiner2020"
Warning--I didn't find a database entry for "kalnay1996"
Warning--I didn't find a database entry for "wheeler2004"
Warning--I didn't find a database entry for "naujokat1973"
Warning--I didn't find a database entry for "smith1953"
Warning--I didn't find a database entry for "ern2017"
You've used 48 entries,
            2932 wiz_defined-function locations,
            1364 strings with 19866 characters,
and the built_in function-call counts, 51639 in all, are:
= -- 5917
> -- 1969
< -- 16
+ -- 930
- -- 613
* -- 3256
:= -- 5062
add.period$ -- 48
call.type$ -- 48
change.case$ -- 301
chr.to.int$ -- 49
cite$ -- 144
duplicate$ -- 5177
empty$ -- 2549
format.name$ -- 770
if$ -- 10164
int.to.chr$ -- 1
int.to.str$ -- 1
missing$ -- 533
newline$ -- 153
num.names$ -- 192
pop$ -- 2168
preamble$ -- 1
purify$ -- 300
quote$ -- 0
skip$ -- 3149
stack$ -- 0
substring$ -- 2288
swap$ -- 4575
text.length$ -- 1
text.prefix$ -- 0
top$ -- 0
type$ -- 429
warning$ -- 0
while$ -- 254
width$ -- 0
write$ -- 581
(There were 6 error messages)
