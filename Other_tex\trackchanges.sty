%%
%% This file is part of TrackChanges
%% Copyright 2006-2009 
%%    Novimir <PERSON> Pablant <<EMAIL>>
%%    <PERSON> <<EMAIL>>
%%
%% http://trackchanges.sourceforge.net/
%%
%% Portions of this code adapted from the 'soul' package.
%% Copyright (C) 1998-2003 Melchior FRANZ
%%
%% TrackChanges is free software: you can redistribute it and/or modify
%% it under the terms of the GNU General Public License as published by
%% the Free Software Foundation, either version 2 of the License, or
%% (at your option) any later version.
%%
%% TrackChanges is distributed in the hope that it will be useful,
%% but WITHOUT ANY WARRANTY; without even the implied warranty of
%% MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
%% GNU General Public License for more details.
%%
%% You should have received a copy of the GNU General Public License
%% along with TrackChanges.  If not, see <http://www.gnu.org/licenses/>.
%%
%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% trackchanges.sty
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% A graphical interface has been written to allow the changes made
%% using this style file to be interactively accepted, rejected 
%% or changed.
%%   trackchanges.py
%%
%% To find the latest versions this latex style file and the 
%% associated files file go to:
%%   http://trackchanges.sourceforge.net/
%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% Usage:
%%
%%   Help files are included as part of this package:
%%     ./documentation/web/html_css/help.html
%%
%%   TrackChanges adds the following 5 commands to allow for collaborative 
%%   editing of LaTeX documents.
%%
%%     \add[editor]{text to add}
%%     \remove[editor]{text to remove}
%%     \change[editor]{text to remove}{text to add}
%%     \annote[editor]{text to annotate}{note}
%%     \note[editor]{note}
%%  
%%   In all cases 'editor' is optional.
%% 
%%   In order to have comments color coded by editor, the editors must
%%   be added in the preamble.
%%
%%     \addeditor{editor}
%%
%% 
%% Options
%%
%%   There are 5 display options. These are specified with the
%%   \usepackage[options]{trackchanges)
%%
%%     finalold
%%        Ignore all of the edits.  
%%        The document will look as if the edits had not been added.
%%     finalnew
%%        Accept all of the edits. 
%%        Notes will not be shown in the final output. 
%%     footnotes
%%        Added text will be shown inline. Removed text and notes
%%        will be shown as footnotes.
%%        This is the default option.
%%     margins
%%        Added text will be shown inline. Removed text and notes 
%%        will be shown in the margin.
%%        Margin notes will be aligned with the edits when possible.
%%     inline
%%        All edits will be shown inline.
%%
%%   The following two options are to be used with the 'margins' 
%%   display style.  They are used to make more space for the margin
%%   notes.  By default the margins are left as in the original
%%   document.
%%
%%     movemargins
%%        Move the text over to the left hand side.
%%        The textwidth will stay the same.
%%     adjustmargins
%%        Reduce the textwidth and move the text over to the left 
%%        hand side.This option will produced the most space for 
%%        the margin notes.
%%
%%
%% Advanced Usage
%%
%%   Certain types of commands cannot be used inside of the TrackChanges
%%   edit commands without special handling.  For more details see
%%   the limitations section.
%%
%%   This option can be used to deal with incompatible commands.
%%
%%     ignoremode
%%
%%   Additional commands can be added to the ignore list with the
%%   following command.
%%
%%     \tcignore{<command name>}{<num args>}{<arg num to pass>}
%%
%%   The last argument specifies which, if any, arguments to pass
%%   as plain text when the command is ignored.  If the last
%%   argument is zero, then all of the arguments will be ignored.
%%   If \color and \textcolor were not already on the ignore 
%%   list then the following commands could be used to add them:
%%
%%     \tcignore{\textcolor}{2}{2}
%%     \tcignore{\color}{1}{0}
%%
%%
%%   TrackChanges also requires special handling for font changing
%%   commands.  These type of commands may have to be registered
%%   with track changes to be dealt with properly.  To register a
%%   new command use the following command.
%%
%%     \tcregister{<command name>}{<num args>}
%%
%%   All of the standard font switching command are registered by 
%%   default.  If \bf and \emph were not already registered then
%%   the following commands could be used to add them:
%%
%%     \tcregister{\bf}{0}
%%     \tcregister{\emph}{1}
%%
%%   While register commands is primarily for font changing it may
%%   work for other types of commands that are causing problems.
%%
%%
%%
%% Limitations
%%
%%   - If another package is used that adjusts the margins, it
%%     can take precedence over the TrackChanges handling.
%%     To make sure TrackChanges has the last word, load it 
%%     after all other margin related packages.
%%
%%   - Incompatable with the package 'ulem'
%%     Use the package 'soul' instead for underlining/strikeout.
%%
%%   - TrackChanges cannot handle certain kinds of commands inside
%%     the edit commands.  In particular \color, \textcolor and 
%%     any commands from the underlining package 'soul' will not work.  
%%     There are two ways to get around these issues.
%%     1. The 'ignoremode' option can be used.
%%          This will tell TrackChanges to ignore certain commands
%%          (Including the ones mentioned above).
%%          Additional commands can be added to the ignore list with
%%          the \tcignore command.
%%     2. Sometimes the problematic commands can be preceded by 
%%        \protect. This however can have strange behavior.
%% 
%%   - TrackChanges requires special handling for certain kinds of
%%     commands inside of edit commands.  These problematic commands
%%     are generally font switching commands.  All of the common
%%     font switching commands (such as \emph, \textbf, etc.)
%%     are already handled.  If you run into an issue you can try
%%     using \tcregister to register the problematic command.
%%
%%   - No easy way to tell TrackChanges not to split footnotes 
%%     across multiple pages.
%%
%%   - Some other packages that take over footnote handling may be
%%     incompatible with TrackChanges.  Using a footnote package
%%     such as footmisc may fix these problems.
%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% trackchanges version 0.6,   2007-06-13
%% written by Felix Salfner
%%
%% trackchanges version 0.7,   2009-03-09
%% rewritten by Novimir Pablant
%%
%% version 0.7.0, 2009-04-22 - Novimir Pablant
%%     Overhauled how the editors are set up.  Now each editor can be
%%       added simply by using the /addeditor{name} command.
%%     Color system completely overhauled, now uses the 'soul' package.  
%%     Added margin commands 'movemargins' and 'adjustmargins'.
%%     Added an option ignoremode and a reimplementation of portions
%%       of 'soul' to allow commands to be ignored inside edits.
%%       Commands \tcignore and \tcregister were added.
%%     Added depreciation warnings for TrackChanges v0.6 commands.
%%     Numerous other internal changes.
%%
%%
%% ---------------------------------------------------------------------
%% To do:
%%   - Add ability to set custom colors.
%%
%%   - Create a better way of adding editors that does not have a 
%%     five editor limit.
%%
%%   - Check if the package 'ulem' is loaded, and issue an error.
%%     Right now I am checking if the \ulem command exists.
%%     and issuing a warning.
%%
%%   - Better error handling, error messages.
%%
%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\NeedsTeXFormat{LaTeX2e}[1995/12/01]
\ProvidesPackage{trackchanges}
   [2009/04/22 v0.7.0 TrackChanges file ]

% Setup the required packages.
\RequirePackage{color}
\RequirePackage{ifthen}
\RequirePackage{calc}
\RequirePackage{soul}
\RequirePackage{morefloats}

% I ran into some conflicts with other packages that deal 
% with footnotes.  Adding footmisc solved those problems for me.
%\RequirePackage{footmisc}
%\RequirePackage{manyfoot}

%%
%% -------------- Setup the options
\newif\if@trackchanges
\@trackchangestrue
\newif\if@trackinmargins
\@trackinmarginsfalse
\newif\if@trackinline
\@trackinlinefalse
\newif\if@keepnew
\@keepnewfalse
\newif\if@movemargins
\@movemarginsfalse
\newif\if@adjustmargins
\@adjustmarginsfalse
\newif\if@TCignoremode
\@TCignoremodefalse

\DeclareOption{finalold}{\@trackchangesfalse\@keepnewfalse}
\DeclareOption{finalnew}{\@trackchangesfalse\@keepnewtrue}
\DeclareOption{margins}{\@trackchangestrue\@trackinmarginstrue\@trackinlinefalse}
\DeclareOption{inline}{\@trackchangestrue\@trackinlinetrue\@trackinmarginsfalse}
\DeclareOption{footnotes}{\@trackchangestrue\@trackinmarginsfalse\@trackinlinefalse}

\DeclareOption{movemargins}{\@movemarginstrue\@adjustmarginsfalse}
\DeclareOption{adjustmargins}{\@adjustmarginstrue\@movemarginstrue}

\DeclareOption{ignoremode}{\@TCignoremodetrue}
\DeclareOption{ignoremodeoff}{\@TCignoremodefalse}

%% This option exists for backwards compatibility with v0.6.
%% A depreciated warning will be displayed.
\DeclareOption{final}{\@trackchangesfalse\@keepnewfalse
  \PackageWarningNoLine{trackchanges}{%
    Option 'final' is depreciated.\MessageBreak%
    Use 'finalold' instead%
  }%
}

\ProcessOptions


%%
%% -------------- Inhibit footnotes getting split across pages
%% This will inhibit splitting completely.
%% LaTeX will complain about `Underfull \vbox' unless 
%% \raggedbottom is also used.
%
%\interfootnotelinepenalty=10000
%
%% Default value is 100.


%%
%% -------------- Set options for the soul package
\setstcolor{red}
\setuloverlap{0pt}

%%
%% -------------- Make an alias to the 'soulregister' command.
\let\tcregister\soulregister

%% -------------- Setup new macros for the soul package.
%% Here we setup two new commands using soul package macros.
%% These will allow strikeout, and underline with colored
%% text.  These are important as they ensure that color is
%% maintained even in situations such as a footnote getting
%% split across pages.

%% This is where the color is actually added.
\def\SOUL@coloreverysyllable{%
  \SOUL@textcolor{\the\SOUL@syllable%
    \SOUL@setkern\SOUL@charkern%
  }%
}

%% This is where the color is actually added.
\def\SOUL@ulcoloreverysyllable{%
  \SOUL@textcolor{\SOUL@uleverysyllable}%
}

%% Here we define a new command with the text color.
\def\SOUL@settextcolor#1{%
  \def\SOUL@textcolor{\textcolor{#1}}%
}

%% This is the setup command for colored text.
\def\SOUL@colorsetup#1{%
  \SOUL@settextcolor{#1}%
  \SOUL@setup%
  \let\SOUL@everysyllable\SOUL@coloreverysyllable%
}

%% This is the setup command for strikeout with colored text.
\def\SOUL@stcolorsetup#1{%
  \SOUL@settextcolor{#1}%
  \SOUL@stsetup%
  \let\SOUL@everysyllable\SOUL@ulcoloreverysyllable%
}

%% This is the setup command for underline with colored text.
\def\SOUL@ulcolorsetup#1{%
  \SOUL@settextcolor{#1}%
  \SOUL@ulsetup%
  \let\SOUL@everysyllable\SOUL@ulcoloreverysyllable%
}

%% Here I define the new callables.

%% Color with strikeout
\DeclareRobustCommand*{\stcolor}[1]{%
  \SOUL@stcolorsetup{#1}%
  \SOUL@%
}

%% Color with underline
\DeclareRobustCommand*{\ulcolor}[1]{%
  \SOUL@ulcolorsetup{#1}%
  \SOUL@%
}

%% Colored text
\DeclareRobustCommand*{\soulcolor}[1]{%
  \SOUL@colorsetup{#1}%
  \SOUL@%
}

%%
%% -------------- Setup counters
\newcounter{userid}
\setcounter{userid}{-1}
\newcounter{changenumber}[page]
\setcounter{changenumber}{0}
\newcounter{storefootnote}
\newcounter{numeditors}
\setcounter{numeditors}{0}
\newcounter{maxeditors}
\setcounter{maxeditors}{5}


%%
%% ----------- Create commands for five users
\newcommand{\TC@editorOne}{n/n}
\newcommand{\TC@editorTwo}{n/n}
\newcommand{\TC@editorThree}{n/n}
\newcommand{\TC@editorFour}{n/n}
\newcommand{\TC@editorFive}{n/n}


%%
%% ----------- create commands to add an editor.
\newcommand{\addeditor}[1]{
  \matchuser{#1}
  \ifthenelse{\value{userid} = -1}
      {\addNewEditor{#1}}{}
}
\newcommand{\addNewEditor}[1]{
  \stepcounter{numeditors}
  \setcounter{userid}{\value{numeditors}}
  % Check the number of authors.
  \ifthenelse{\value{numeditors} > \value{maxeditors}}
      {\TC@WarningTooManyEditors}
      {}
  \ifthenelse{\value{userid} = 1}
      {\renewcommand{\TC@editorOne}{#1}}
      {\ifthenelse{\value{userid} = 2}
        {\renewcommand{\TC@editorTwo}{#1}}
        {\ifthenelse{\value{userid} = 3}
          {\renewcommand{\TC@editorThree}{#1}}
          {\ifthenelse{\value{userid} = 4}
            {\renewcommand{\TC@editorFour}{#1}}
            {\ifthenelse{\value{userid} = 5}%
              {\renewcommand{\TC@editorFive}{#1}}
              {}
            }
          }
        }
      }
}


%%
%% --------------- define text colors for different users
\newcommand{\usercolor}{%
   \ifthenelse{\value{userid} = 0}%
       {\definecolor{UserColor}{rgb}{0.33,0.11,0.46}}% Purple
       {\ifthenelse{\value{userid} = 1}%
         {\definecolor{UserColor}{rgb}{0.1,0.1,0.8}}% Blue
         {\ifthenelse{\value{userid} = 2}%
           {\definecolor{UserColor}{rgb}{0.0,0.6,0.0}}% Green
           {\ifthenelse{\value{userid} = 3}%
             {\definecolor{UserColor}{rgb}{0.68,0.17,0.5}}% Magenta
             {\ifthenelse{\value{userid} = 4}%
               {\definecolor{UserColor}{rgb}{0.13,0.60,0.65}}% Cyan
               {\ifthenelse{\value{userid} = 5}%
                 {\definecolor{UserColor}{rgb}{0.86,0.50,0.12}}% Orange
                 {\definecolor{UserColor}{rgb}{0.13,0.70,0.50}}% Teal
               }%
             }%
           }%
         }%
       }%
}


%%
%% -------------- set counter userid according to editor
\newcommand{\matchuser}[1]{%
   \ifthenelse{\equal{#1}{}}%
      {\setcounter{userid}{0}}%
      {\ifthenelse{\equal{#1}{\TC@editorOne}}%
        {\setcounter{userid}{1}}%
        {\ifthenelse{\equal{#1}{\TC@editorTwo}}%
          {\setcounter{userid}{2}}%
          {\ifthenelse{\equal{#1}{\TC@editorThree}}%
            {\setcounter{userid}{3}}%
            {\ifthenelse{\equal{#1}{\TC@editorFour}}%
              {\setcounter{userid}{4}}%
              {\ifthenelse{\equal{#1}{\TC@editorFive}}%
                {\setcounter{userid}{5}}%
                {\setcounter{userid}{-1}}%
              }%
            }%
          }%
        }%
      }%
   \usercolor%
}

%% --------------- Setup a command for the user label.
\newcommand{\UserLabel}{}
%%
\newcommand{\GetUserLabel}[1]{%
  \ifthenelse{\equal{#1}{}}%
             {}%
             {\userco{\textit{#1}:}}%
}


%%
%% --------------- define commands to color text.
%%                 The first command works properly across paragraphs.
%%                 The second works well with the footnote.
%%                 
%%                 The last two use the soul package.
\newcommand{\usertext}[1]{\color{UserColor}{#1}\normalcolor}
\newcommand{\usertextfoot}[1]{\textcolor{UserColor}{#1}}
\newcommand{\userco}[1]{\soulcolor{UserColor}{#1}}
\newcommand{\userst}[1]{\stcolor{UserColor}{#1}}
\newcommand{\userul}[1]{\ulcolor{UserColor}{#1}}


%%
%% --------------- define command for margin text.
\newcommand{\marginText}[2]{%
  \renewcommand{\baselinestretch}{0.8}%
  \marginpar{\raggedright%
             \footnotesize%
             \textsuperscript{\thefootnote}%
             \UserLabel~#2%
  }%
}


%%
%% --------------- This need to be called at the beginning of every command.
\newcommand{\trackingstart}[1]{%
  % Set the ignores if the 'ignore' option was set.
  \if@TCignoremode%
    \TC@SetIgnores%
  \fi%
  \setcounter{storefootnote}{\value{footnote}}%
  \refstepcounter{changenumber}%
  \matchuser{#1}%
  \ifthenelse{\value{userid} = -1}%
      {\TC@WarningUnknownEditor{#1}}%
      {}%
  \renewcommand{\thefootnote}{\usertextfoot{c\arabic{changenumber}}}%
  \setulcolor{UserColor}%
  \renewcommand{\UserLabel}{\GetUserLabel{#1}}%
}


%%
%% --------------- This need to be called at the end of every command.
\newcommand{\trackingend}{%
  \setcounter{footnote}{\value{storefootnote}}%
  % Reset the ignores if the 'ignore' option was set.
  \if@TCignoremode%
    \TC@ResetIgnores%
  \fi%
}


%%
%% --------------- Define the frontend command: \add
%%                 usage: \add[Editor]{new text}
\newcommand{\add}[2][]{%
  \if@trackchanges%
    \trackingstart{#1}%
    \if@trackinmargins%
      \textsuperscript{\thefootnote}%
      \marginText{#1}{\userco{\textit{Text added.}}}%
    \else%
      \if@trackinline%
          {\footnotesize\textsuperscript{\UserLabel}}%
      \else%
        \footnote{\UserLabel~\userco{\textit{Text added.}}}%
      \fi%
    \fi%
    \usertext{\ul{#2}}%
    \trackingend%
  \else%
    %% if trackchanges is false ...
    \if@keepnew%
      %% Keep the new text.
      #2%
    \fi%
    %% if keepnew is false do nothing.
  \fi%
}


%%
%% --------------- Define the frontend command: \remove
%%                 usage: \remove[Editor]{original text}
\newcommand{\remove}[2][]{%
  \if@trackchanges%
    \trackingstart{#1}%
    \if@trackinmargins%
      \textsuperscript{\thefootnote}%
      \marginText{#1}{\userst{#2}}%
    \else%
      \if@trackinline%
        {\footnotesize\textsuperscript{\UserLabel}\usertext{\st{#2}}}%     
      \else%
        \footnote{\UserLabel~\userst{#2}}%
      \fi%
    \fi%
    \trackingend%
  \else%
    %% if trackchanges is false ...
    \if@keepnew%
      %% Remove the old text.
    \else
      %% Keep the old text.
      #2%
    \fi%
  \fi%
}


%%
%% --------------- Define the frontend command: \change
%%                 usage: \change[Editor]{original text}{new text}
\newcommand{\change}[3][]{%
  \if@trackchanges%
    \trackingstart{#1}%
    \if@trackinmargins%
      \textsuperscript{\thefootnote}%
      \marginText{#1}{\userst{#2}}%
    \else%
      \if@trackinline%
        {\footnotesize\textsuperscript{\UserLabel}\usertext{\st{#2}}}% 
      \else%
        \footnote{\UserLabel~\userst{#2}}%
      \fi%
    \fi%
    \usertext{\ul{#3}}%
    \trackingend%
  \else%
    %% if trackchanges is false ...
    \if@keepnew%
      %% Keep the new text.
      #3%
    \else
      %% Keep the old text.
      #2%
    \fi%
  \fi%
}


%%
%% --------------- Define frontend command: \annonte
%%                 usage: \annote[Editor]{highlighted text}{note text}
\newcommand{\annote}[3][]{%
  % Make the underlining thicker and higher.
  \setul{1pt}{0.3ex}%
  \if@trackchanges%
    \trackingstart{#1}%
    \if@trackinmargins%
      \textsuperscript{\thefootnote}%
      \marginText{#1}{\userco{#3}}%
      \ul{#2}%
    \else%
      \if@trackinline%
        {\footnotesize\textsuperscript{\UserLabel}}%
        \ul{#2}%
        {\footnotesize\usertext{~[#3]}}%
      \else%
        \footnote{\UserLabel~\userco{#3}}%
        \ul{#2}%
      \fi%
    \fi%
    \trackingend%
  \else%
    %% if trackchanges is false ... just print text.
    #2%
  \fi%
  % Reset the underlineing
  \resetul%
}


%%
%% --------------- Define frontend command: \note
%%                 usage: \note[Editor]{note text}
\newcommand{\note}[2][]{%
  \if@trackchanges%
    \trackingstart{#1}%
    \if@trackinmargins%
      \textsuperscript{\thefootnote}%
      \marginText{#1}{\userco{#2}}%
    \else%
      \if@trackinline%
        {\footnotesize\textsuperscript{\UserLabel}}%
        {\footnotesize\usertext{~[#2]}}%
      \else%
        \footnote{\UserLabel~\userco{#2}}%
      \fi%
    \fi%
    \trackingend%
  \else%
    %% if trackchanges is false ... do not print notes
  \fi%
}


%%
%% --------------- Define frontend command: \refneeded
%%                 usage: \refneeded[Editor]{note text}
\newcommand{\refneeded}[2][]{%
  \if@trackchanges%
    \trackingstart{#1}%
    \if@trackinmargins%
      \textsuperscript{\thefootnote}%
      \marginText{#1}{\userco{REFERENCE NEEDED #2}}%
    \else%
      \if@trackinline%
        {\footnotesize\textsuperscript{\UserLabel}}%
        \ifthenelse{\equal{#2}{}}%
                   {{\footnotesize\usertext{~[REFERENCE NEEDED]}}}%
                   {{\footnotesize\usertext{~[REFERENCE NEEDED #2]}}}%
      \else%
        \footnote{\UserLabel~\userco{REFERENCE NEEDED #2}}%
      \fi%
    \fi%
    \trackingend%
  \else%
    %% if trackchanges is false ... do not print notes
  \fi%
}


%%
%% -------------- Setup for margin notes.
\newcommand{\setupMarginpar}{
  \setlength{\marginparwidth}{\paperwidth}
  \addtolength{\marginparwidth}{-1in}
  \addtolength{\marginparwidth}{-\oddsidemargin}
  \addtolength{\marginparwidth}{-\hoffset}
  \addtolength{\marginparwidth}{-\textwidth}
  \addtolength{\marginparwidth}{-\marginparsep}
  \addtolength{\marginparwidth}{-5mm}
}
\newcommand{\setupMoveMargins}{
  \setlength{\oddsidemargin}{-1in}
  \addtolength{\oddsidemargin}{0.5in}
}
\newcommand{\setupAdjustMargins}{
  \setlength{\textwidth}{0.5\paperwidth}
}
\newcommand{\setupMargins}{
  \if@movemargins
    \setupMoveMargins{}
  \fi
  \if@adjustmargins
    \setupAdjustMargins{}
  \fi
  % The Margins are setup using \AtBeginDocument.
}

%% --------------- Check that conflicting packages are not loaded.
\def\TC@checkCompatibility{
  \ifx\uline\undefined
  \else
    \TC@WarningUlem
  \fi
}

%% ---------------- Setup for actions to be taken after the style file has been read.
\AtEndOfPackage{
  \if@trackchanges
    \if@trackinmargins
      \setupMargins{}
    \else
    \fi
  \fi
}

%% ---------------- Setup for actions to be taken after the preamble.
\AtBeginDocument{
  \TC@checkCompatibility
  \TC@checkDepreciated 
  \if@trackchanges
    \if@trackinmargins
      \setupMarginpar
    \else
    \fi
  \fi
}

%%
%% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% --------------- Setup for backwards compatibility with v0.6
\newcommand{\initialsOne}{}
\newcommand{\initialsTwo}{}
\newcommand{\initialsThree}{}
\newcommand{\initialsFour}{}
\newcommand{\initialsFive}{}

%% --------------- Check for depreciated options
\def\TC@checkDepreciated{
  \ifthenelse{\equal{\initialsOne}{}}
    {}{\TC@WarningDepreciatedInitials
       \addeditor{\initialsOne}}
  \ifthenelse{\equal{\initialsTwo}{}}
    {}{\TC@WarningDepreciatedInitials
       \addeditor{\initialsTwo}}
  \ifthenelse{\equal{\initialsThree}{}}
    {}{\TC@WarningDepreciatedInitials
       \addeditor{\initialsThree}}
  \ifthenelse{\equal{\initialsFour}{}}
    {}{\TC@WarningDepreciatedInitials
       \addeditor{\initialsFour}}
  \ifthenelse{\equal{\initialsFive}{}}
    {}{\TC@WarningDepreciatedInitials
       \addeditor{\initialsFive}}
}

%%
%% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% --------------- Setup warnings.
\newcommand{\TC@WarningTooManyEditors}{%
  \PackageWarningNoLine{trackchanges}{%
    A maximum of \arabic{maxeditors} editors are supported.\MessageBreak%
    Changes made by additional editors will all have\MessageBreak%
    the same color coding%
  }%
}
\newcommand{\TC@WarningUnknownEditor}[1]{%
  \PackageWarning{trackchanges}{%
    Editor '#1' unknown.\MessageBreak%
  }%
}
\newcommand{\TC@WarningUlem}{%
  \PackageWarningNoLine{trackchanges}{%
    TrackChanges is not compatable with the \MessageBreak%
    'ulem' package.\MessageBreak%
    Use the 'soul' package instead%
  }%
}
\newcommand{\TC@WarningDepreciatedInitials}{%
  \PackageWarningNoLine{trackchanges}{%
    The '\noexpand\renewcommand{\noexpand\initialsNum}{name}' commands\MessageBreak%
    are depreciated.\MessageBreak%
    Use '\noexpand\addeditor{name}' instead.'%
  }%
}


%% 
%% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% Below are a set of definitions to add an ignore list to the soul
%% package. 
%%
%% This is not a default as it reimplements some of the internals of
%% the soul package.  If a new version of soul were to come out 
%% this stuff might break.
\if@TCignoremode


%% These two function allow us to turn on and off the ignore list.
%% This is so we don't mess up soul for anyone else.
\def\TC@SetIgnores{%
  \let\SOUL@ignores\TC@ignores%
}
\def\TC@ResetIgnores{%
  \let\SOUL@ignores\TC@emptyignores%
}

\newtoks\TC@ignores
\newtoks\TC@emptyignores

%% The commands are separated by \\. The first number is the 
%% number of inputs for the command. The command name
%% is given next.  The last number specifies which input to 
%% pass through.  If the last number is zero nothing will be
%% passed.  

\TC@ignores={%
  \\2\textcolor{2}%
  \\1\color{0}%
  \\1\ul{1}%
  \\1\st{1}%
  \\1\hl{1}%
  \\1\caps{1}%
  \\1\so{1}%
}
\TC@emptyignores={}

%% Initialize soul not to use the ignore list.
\let\SOUL@ignores\TC@emptyignores

%% This is a way to register new ignores.
\def\tcignore#1#2#3{{%
    \edef\x{\global\TC@ignores={\the\TC@ignores
        \noexpand\\#2\noexpand#1#3}}\x
}}


%% Here I redefine \SOUL@dotoken.  
%% The new version also checks for ignores.
\def\SOUL@dotoken#1{%
  \def\SOUL@@{\SOUL@addtoken{#1}}%
  \def\\##1##2{%
    \edef\SOUL@x{\string#1}%
    \edef\SOUL@n{\string##2}%
    \ifx\SOUL@x\SOUL@n
      \def\SOUL@@{\SOUL@docmd{##1}{#1}}%
    \else
      \edef\SOUL@n{\string##2\space}%
      \ifx\SOUL@x\SOUL@n
        \def\SOUL@@{\SOUL@docmd{##1}{#1}}%
      \fi
    \fi
  }%
  \the\SOUL@cmds
  \def\\##1##2##3{%
    \edef\SOUL@x{\string#1}%
    \edef\SOUL@n{\string##2}%
    \ifx\SOUL@x\SOUL@n
      \def\SOUL@@{\SOUL@doignore{##1}{#1}{##3}}%
    \else
      \edef\SOUL@n{\string##2\space}%
      \ifx\SOUL@x\SOUL@n
        \def\SOUL@@{\SOUL@doignore{##1}{#1}{##3}}%
      \fi
    \fi
  }%
  \the\SOUL@ignores%
  \SOUL@@
}

%% This is where the actual ignoring is done.
\def\SOUL@doignore#1#2#3{%
  \ifx2#1%
    \SOUL@doword%
    \def\SOUL@@##1##2{%
      \ifx1#3%
        \SOUL@do{##1}%
      \else\ifx2#3%
        \SOUL@do{##2}%
      \fi\fi%
      \SOUL@scan%
    }%
  \else\ifx1#1%
    \SOUL@doword%
    \def\SOUL@@##1{%
      \ifx1#3%
        \SOUL@do{##1}%
      \fi%
      \SOUL@scan%
    }%
  \else
    \SOUL@doword
    \let\SOUL@@\SOUL@scan
  \fi\fi
  \SOUL@@
}

\fi

%%
%%
%% End of file trackchanges.sty