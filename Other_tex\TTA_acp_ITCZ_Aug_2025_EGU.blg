This is BibTeX, Version 0.99d (TeX Live 2024)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: TTA_acp_ITCZ_Aug_2025_EGU.aux
The style file: copernicus.bst
Database file #1: Ref_ITCZ.bib
I was expecting a `,' or a `}'---line 169 of file Ref_ITCZ.bib
 : 
 : @incollection{reichler2016,
(<PERSON><PERSON><PERSON> may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 231 of file Ref_ITCZ.bib
 : 
 : doi = {10.1175/1520-0442(1993)006<2162:ASDCOT>2.0.CO;2},
(<PERSON><PERSON><PERSON> may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 289 of file Ref_ITCZ.bib
 : 
 : doi={10.1175/2007JAS2518.1}
(<PERSON>rro<PERSON> may have been on previous line)
I'm skipping whatever remains of this entry
Repeated entry---line 1363 of file Ref_ITCZ.bib
 : @article{byrne2018
 :                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 1381 of file Ref_ITCZ.bib
 : @article{gu2002
 :                ,
I'm skipping whatever remains of this entry
Repeated entry---line 2031 of file Ref_ITCZ.bib
 : @article{wei2024
 :                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 2343 of file Ref_ITCZ.bib
 : @article{ern2014
 :                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 2591 of file Ref_ITCZ.bib
 : @article{kerns2018
 :                   ,
I'm skipping whatever remains of this entry
Warning--empty journal in hoffmann2021
Warning--can't use both volume and number fields in rakhman2017
You've used 82 entries,
            2932 wiz_defined-function locations,
            1554 strings with 27877 characters,
and the built_in function-call counts, 90625 in all, are:
= -- 10499
> -- 3325
< -- 33
+ -- 1578
- -- 1033
* -- 5608
:= -- 8724
add.period$ -- 82
call.type$ -- 82
change.case$ -- 509
chr.to.int$ -- 83
cite$ -- 248
duplicate$ -- 9182
empty$ -- 4484
format.name$ -- 1306
if$ -- 17932
int.to.chr$ -- 1
int.to.str$ -- 1
missing$ -- 915
newline$ -- 255
num.names$ -- 328
pop$ -- 3608
preamble$ -- 1
purify$ -- 508
quote$ -- 0
skip$ -- 5876
stack$ -- 0
substring$ -- 4106
swap$ -- 8152
text.length$ -- 7
text.prefix$ -- 0
top$ -- 0
type$ -- 732
warning$ -- 2
while$ -- 439
width$ -- 0
write$ -- 986
(There were 8 error messages)
