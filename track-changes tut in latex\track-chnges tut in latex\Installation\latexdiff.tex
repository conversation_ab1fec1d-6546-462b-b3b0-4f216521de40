%SCRIPT

var cf = app.getCurrentFileName();

var ld = new String(cf);
ld=ld.substring(0,ld.lastIndexOf("/"));

information("old version");
fileChooser.setDir(ld);
fileChooser.setFilter("TeX files (*.tex)");
fileChooser.exec();
var fold=new String();
fold=fileChooser.fileName();
fold=fold.substring(fold.lastIndexOf("/")+1);

information("new version");
fileChooser.setDir(ld);
fileChooser.setFilter("TeX files (*.tex)");
fileChooser.exec();
var fnew=new String();
fnew=fileChooser.fileName();
fnew=fnew.substring(fnew.lastIndexOf("/")+1);

information("diff file by Chas");
fileChooser.setDir(ld);
fileChooser.setFilter("TeX files (*.tex)");
fileChooser.exec(fout);
var fout=new String();
fout=fileChooser.fileName();
fout=fout.substring(fout.lastIndexOf("/")+1);

ldfout = ld+"\\"+fout;

var cmdstr = new String();
cmdstr = "latexdiff-so "+fold+" "+fnew+" > /dev/null";
var proc = system("cmd /C "+cmdstr,ld);
proc.waitForFinished();
writeFile(ldfout, proc.readAllStandardOutputStr());
app.load(ldfout); // load diff file
buildManager.runCommand("txs:///quick", ldfout);

delete(cmdstr);
delete(dialog);
delete(fold);
delete(fnew);
delete(fout);
delete(ld);
delete(ldfout);
