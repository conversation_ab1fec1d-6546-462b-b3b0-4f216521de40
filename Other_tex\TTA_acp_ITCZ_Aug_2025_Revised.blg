This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: TTA_acp_ITCZ_Aug_2025_Revised.aux
Reallocating 'name_of_file' (item size: 1) to 11 items.
The style file: copernicus.bst
Reallocating 'name_of_file' (item size: 1) to 9 items.
Database file #1: Ref_ITCZ.bib
I was expecting a `,' or a `}'---line 169 of file Ref_ITCZ.bib
 : 
 : @incollection{reichler2016,
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 231 of file Ref_ITCZ.bib
 : 
 : doi = {10.1175/1520-0442(1993)006<2162:ASDCOT>2.0.CO;2},
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 289 of file Ref_ITCZ.bib
 : 
 : doi={10.1175/2007JAS2518.1}
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
Repeated entry---line 2343 of file Ref_ITCZ.bib
 : @article{ern2014
 :                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 3063 of file Ref_ITCZ.bib
 : @article{fritts2003
 :                    ,
I'm skipping whatever remains of this entry
You're missing a field name---line 3185 of file Ref_ITCZ.bib
 : year = {2008}, 
 :                % Although cited as 2011, BAMS has this as 2008
I'm skipping whatever remains of this entry
You're missing a field name---line 3365 of file Ref_ITCZ.bib
 : year = {2010}, 
 :                % Note: Cited as 2009, but QJ paper is 2010
I'm skipping whatever remains of this entry
Warning--I didn't find a database entry for "pfister1993"
Warning--I didn't find a database entry for "alexander2004"
Warning--I didn't find a database entry for "kim2009"
Warning--I didn't find a database entry for "mamalakis2021"
Warning--I didn't find a database entry for "zheng2011"
Warning--I didn't find a database entry for "anthes2011"
Warning--I didn't find a database entry for "wang2017"
Warning--I didn't find a database entry for "yang2019"
Warning--I didn't find a database entry for "calvo2010"
You've used 29 entries,
            2932 wiz_defined-function locations,
            1254 strings with 16030 characters,
and the built_in function-call counts, 34937 in all, are:
= -- 4133
> -- 1305
< -- 8
+ -- 632
- -- 409
* -- 2141
:= -- 3314
add.period$ -- 29
call.type$ -- 29
change.case$ -- 195
chr.to.int$ -- 30
cite$ -- 87
duplicate$ -- 3538
empty$ -- 1641
format.name$ -- 503
if$ -- 6879
int.to.chr$ -- 1
int.to.str$ -- 1
missing$ -- 337
newline$ -- 96
num.names$ -- 116
pop$ -- 1496
preamble$ -- 1
purify$ -- 194
quote$ -- 0
skip$ -- 2186
stack$ -- 0
substring$ -- 1640
swap$ -- 3215
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 261
warning$ -- 0
while$ -- 164
width$ -- 0
write$ -- 356
(There were 7 error messages)
