%%%%%%%%%%%%%%%%%%%%%%%%%%Track changes tut by Chas%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%DIF LATEXDIFF DIFFERENCE FILE
%DIF DEL original.tex   Tue Sep 11 12:09:18 2018
%DIF ADD revised.tex    Tue Sep 11 12:48:02 2018
\documentclass[11pt,a4paper]{article}
\usepackage{mathptmx}


\author{ \DIFaddbegin \DIFadd{by }\DIFaddend Chandra Has}
\title{How to Track the Changes made to the Document \DIFaddbegin \DIFadd{using }\LaTeX \DIFadd{diff}\DIFaddend }
\date{} %DIF > 
%DIF PREAMBLE EXTENSION ADDED BY LATEXDIFF
%DIF UNDERLINE PREAMBLE %DIF PREAMBLE
\RequirePackage[normalem]{ulem} %DIF PREAMBLE
\RequirePackage{color}\definecolor{RED}{rgb}{1,0,0}\definecolor{BLUE}{rgb}{0,0,1} %DIF PREAMBLE
\providecommand{\DIFadd}[1]{{\protect\color{blue}\uwave{#1}}} %DIF PREAMBLE
\providecommand{\DIFdel}[1]{{\protect\color{red}\sout{#1}}}                      %DIF PREAMBLE
%DIF SAFE PREAMBLE %DIF PREAMBLE
\providecommand{\DIFaddbegin}{} %DIF PREAMBLE
\providecommand{\DIFaddend}{} %DIF PREAMBLE
\providecommand{\DIFdelbegin}{} %DIF PREAMBLE
\providecommand{\DIFdelend}{} %DIF PREAMBLE
%DIF FLOATSAFE PREAMBLE %DIF PREAMBLE
\providecommand{\DIFaddFL}[1]{\DIFadd{#1}} %DIF PREAMBLE
\providecommand{\DIFdelFL}[1]{\DIFdel{#1}} %DIF PREAMBLE
\providecommand{\DIFaddbeginFL}{} %DIF PREAMBLE
\providecommand{\DIFaddendFL}{} %DIF PREAMBLE
\providecommand{\DIFdelbeginFL}{} %DIF PREAMBLE
\providecommand{\DIFdelendFL}{} %DIF PREAMBLE
%DIF END PREAMBLE EXTENSION ADDED BY LATEXDIFF

\begin{document}\maketitle
\hrulefill

\noindent 
1. Install ActivePerl \\
2. Install the LaTeXdiff package \\

\DIFdelbegin \begin{eqnarray*}
\DIFdel{a = b = c
}\end{eqnarray*}
	%DIFAUXCMD
\DIFdelend %DIF > \begin{eqnarray}
%DIF > a = b = c
%DIF > \end{eqnarray}

	
	
\end{document}































