\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\citation{basha2015}
\citation{torrence1998,moss2016}
\citation{alexander2010,wheeler1999}
\citation{alexander2008}
\bibstyle{plainnat}
\bibdata{Ref_ITCZ.bib}
\bibcite{alexander2008}{{1}{2008}{{<PERSON> et~al.}}{{<PERSON>, <PERSON>~la Torre, and <PERSON><PERSON>ed<PERSON>}}}
\bibcite{alexander2010}{{2}{2010}{{<PERSON> et~al.}}{{<PERSON>, Luna, Llamedo, and de~la Torre}}}
\bibcite{basha2015}{{3}{2015}{{Basha et~al.}}{{Basha, Kishore, Ratnam, Ouarda, Velicogna, and Sutterley}}}
\bibcite{moss2016}{{4}{2016}{{<PERSON> et~al.}}{{<PERSON>, <PERSON>, and <PERSON>}}}
\bibcite{torrence1998}{{5}{1998}{{Torrence and Compo}}{{}}}
\bibcite{wheeler1999}{{6}{1999}{{<PERSON> and <PERSON><PERSON><PERSON>}}{{}}}
\gdef \@abspage@last{3}
