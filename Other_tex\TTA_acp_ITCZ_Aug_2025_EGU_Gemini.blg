This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: TTA_acp_ITCZ_Aug_2025_EGU_Gemini.aux
Reallocating 'name_of_file' (item size: 1) to 11 items.
The style file: copernicus.bst
Reallocating 'name_of_file' (item size: 1) to 13 items.
Database file #1: Ref_ITCZ.bib
I was expecting a `,' or a `}'---line 169 of file Ref_ITCZ.bib
 : 
 : @incollection{reichler2016,
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 231 of file Ref_ITCZ.bib
 : 
 : doi = {10.1175/1520-0442(1993)006<2162:ASDCOT>2.0.CO;2},
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 289 of file Ref_ITCZ.bib
 : 
 : doi={10.1175/2007JAS2518.1}
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
Repeated entry---line 2343 of file Ref_ITCZ.bib
 : @article{ern2014
 :                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 2591 of file Ref_ITCZ.bib
 : @article{kerns2018
 :                   ,
I'm skipping whatever remains of this entry
Warning--I didn't find a database entry for "baldwin2001"
Warning--I didn't find a database entry for "kursinski1997"
Warning--I didn't find a database entry for "antones2009"
Warning--I didn't find a database entry for "bell2021"
Warning--I didn't find a database entry for "kalnay1996"
Warning--I didn't find a database entry for "kistler2001"
Warning--I didn't find a database entry for "wheeler2004"
Warning--I didn't find a database entry for "naujokat1973"
Warning--I didn't find a database entry for "santer2000"
You've used 27 entries,
            2932 wiz_defined-function locations,
            1228 strings with 15209 characters,
and the built_in function-call counts, 30381 in all, are:
= -- 3484
> -- 1183
< -- 9
+ -- 545
- -- 370
* -- 1899
:= -- 2963
add.period$ -- 27
call.type$ -- 27
change.case$ -- 178
chr.to.int$ -- 28
cite$ -- 81
duplicate$ -- 3040
empty$ -- 1485
format.name$ -- 460
if$ -- 6009
int.to.chr$ -- 1
int.to.str$ -- 1
missing$ -- 311
newline$ -- 90
num.names$ -- 108
pop$ -- 1273
preamble$ -- 1
purify$ -- 177
quote$ -- 0
skip$ -- 1895
stack$ -- 0
substring$ -- 1332
swap$ -- 2688
text.length$ -- 2
text.prefix$ -- 0
top$ -- 0
type$ -- 240
warning$ -- 0
while$ -- 142
width$ -- 0
write$ -- 332
(There were 5 error messages)
