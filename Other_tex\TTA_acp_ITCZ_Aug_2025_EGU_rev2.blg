This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: TTA_acp_ITCZ_Aug_2025_EGU_rev2.aux
Reallocating 'name_of_file' (item size: 1) to 11 items.
The style file: copernicus.bst
Reallocating 'name_of_file' (item size: 1) to 13 items.
Database file #1: Ref_ITCZ.bib
I was expecting a `,' or a `}'---line 169 of file Ref_ITCZ.bib
 : 
 : @incollection{reichler2016,
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 231 of file Ref_ITCZ.bib
 : 
 : doi = {10.1175/1520-0442(1993)006<2162:ASDCOT>2.0.CO;2},
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 289 of file Ref_ITCZ.bib
 : 
 : doi={10.1175/2007JAS2518.1}
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
Repeated entry---line 2343 of file Ref_ITCZ.bib
 : @article{ern2014
 :                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 2591 of file Ref_ITCZ.bib
 : @article{kerns2018
 :                   ,
I'm skipping whatever remains of this entry
Warning--I didn't find a database entry for "kim2003"
Warning--I didn't find a database entry for "holton2004"
Warning--I didn't find a database entry for "baldwin2001"
Warning--I didn't find a database entry for "kalnay1996"
Warning--I didn't find a database entry for "wheeler2004"
Warning--I didn't find a database entry for "naujokat1973"
Warning--I didn't find a database entry for "smith1953"
You've used 35 entries,
            2932 wiz_defined-function locations,
            1287 strings with 17170 characters,
and the built_in function-call counts, 38106 in all, are:
= -- 4389
> -- 1442
< -- 12
+ -- 683
- -- 448
* -- 2396
:= -- 3711
add.period$ -- 35
call.type$ -- 35
change.case$ -- 220
chr.to.int$ -- 36
cite$ -- 105
duplicate$ -- 3833
empty$ -- 1872
format.name$ -- 570
if$ -- 7505
int.to.chr$ -- 1
int.to.str$ -- 1
missing$ -- 392
newline$ -- 114
num.names$ -- 140
pop$ -- 1634
preamble$ -- 1
purify$ -- 219
quote$ -- 0
skip$ -- 2309
stack$ -- 0
substring$ -- 1678
swap$ -- 3396
text.length$ -- 1
text.prefix$ -- 0
top$ -- 0
type$ -- 312
warning$ -- 0
while$ -- 188
width$ -- 0
write$ -- 428
(There were 5 error messages)
