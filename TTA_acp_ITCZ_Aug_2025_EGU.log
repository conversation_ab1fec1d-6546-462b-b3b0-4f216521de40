This is pdfTeX, Version 3.141592653-2.6-1.40.26 (MiKTeX 24.4) (preloaded format=pdflatex 2024.8.5)  11 JUN 2025 21:31
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./TTA_acp_ITCZ_Aug_2025_EGU.tex
(TTA_acp_ITCZ_Aug_2025_EGU.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
(copernicus.cls
Document Class: copernicus 2023/12/15 10.1.12 Copernicus papers
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/siunitx\siunitx.sty
Package: siunitx 2024-06-24 v3.3.19 A comprehensive (SI) units package
\l__siunitx_number_uncert_offset_int=\count194
\l__siunitx_number_exponent_fixed_int=\count195
\l__siunitx_number_min_decimal_int=\count196
\l__siunitx_number_min_integer_int=\count197
\l__siunitx_number_round_precision_int=\count198
\l__siunitx_number_lower_threshold_int=\count199
\l__siunitx_number_upper_threshold_int=\count266
\l__siunitx_number_group_first_int=\count267
\l__siunitx_number_group_size_int=\count268
\l__siunitx_number_group_minimum_int=\count269
\l__siunitx_angle_tmp_dim=\dimen141
\l__siunitx_angle_marker_box=\box52
\l__siunitx_angle_unit_box=\box53
\l__siunitx_compound_count_int=\count270

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/translations\transl
ations.sty
Package: translations 2022/02/05 v1.12 internationalization of LaTeX2e packages
 (CN)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.s
ty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count271
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftex
cmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infware
rr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.s
ty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks17
\ex@=\dimen142
))
\l__siunitx_table_tmp_box=\box54
\l__siunitx_table_tmp_dim=\dimen143
\l__siunitx_table_column_width_dim=\dimen144
\l__siunitx_table_integer_box=\box55
\l__siunitx_table_decimal_box=\box56
\l__siunitx_table_uncert_box=\box57
\l__siunitx_table_before_box=\box58
\l__siunitx_table_after_box=\box59
\l__siunitx_table_before_dim=\dimen145
\l__siunitx_table_carry_dim=\dimen146
\l__siunitx_unit_tmp_int=\count272
\l__siunitx_unit_position_int=\count273
\l__siunitx_unit_total_int=\count274
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\array.sty
Package: array 2024/06/14 v2.6d Tabular extension package (FMi)
\col@sep=\dimen147
\ar@mcellbox=\box60
\extrarowheight=\dimen148
\NC@list=\toks18
\extratabsurround=\skip49
\backup@length=\skip50
\ar@cellbox=\box61
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/placeins\placeins.s
ty
Package: placeins 2005/04/18  v 2.2
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/adjustbox\adjustbox
.sty
Package: adjustbox 2022/10/17 v1.3a Adjusting TeX boxes (trim, clip, ...)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xkeyval\xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/xkeyval\xkeyval.t
ex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/xkeyval\xkvutils.
tex
\XKV@toks=\toks19
\XKV@tempa@toks=\toks20

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/xkeyval\keyval.te
x))
\XKV@depth=\count275
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/adjustbox\adjcalc.s
ty
Package: adjcalc 2012/05/16 v1.1 Provides advanced setlength with multiple back
-ends (calc, etex, pgfmath)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/adjustbox\trimclip.
sty
Package: trimclip 2020/08/19 v1.2 Trim and clip general TeX material

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.s
ty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.s
ty
Package: graphics 2024/05/23 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphi
cs.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex
.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen149
\Gin@req@width=\dimen150
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/collectbox\collectb
ox.sty
Package: collectbox 2022/10/17 v0.4c Collect macro arguments as boxes
\collectedbox=\box62
)
\tc@llx=\dimen151
\tc@lly=\dimen152
\tc@urx=\dimen153
\tc@ury=\dimen154
Package trimclip Info: Using driver 'tc-pdftex.def'.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/adjustbox\tc-pdftex
.def
File: tc-pdftex.def 2019/01/04 v2.2 Clipping driver for pdftex
))
\adjbox@Width=\dimen155
\adjbox@Height=\dimen156
\adjbox@Depth=\dimen157
\adjbox@Totalheight=\dimen158
\adjbox@pwidth=\dimen159
\adjbox@pheight=\dimen160
\adjbox@pdepth=\dimen161
\adjbox@ptotalheight=\dimen162

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/ifoddpage\ifoddpage
.sty
Package: ifoddpage 2022/10/18 v1.2 Conditionals for odd/even page detection
\c@checkoddpage=\count276
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/varwidth\varwidth.s
ty
Package: varwidth 2009/03/30 ver 0.92;  Variable-width minipages
\@vwid@box=\box63
\sift@deathcycles=\count277
\@vwid@loff=\dimen163
\@vwid@roff=\dimen164
))
Additional configuration file copernicus.cfg used
(copernicus.cfg)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2024/02/08 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\fleqn.clo
File: fleqn.clo 2016/12/29 v1.2b Standard LaTeX option (flush left equations)
\mathindent=\skip51
Applying: [2015/01/01] Make \[ robust on input line 50.
LaTeX Info: Redefining \[ on input line 51.
Already applied: [0000/00/00] Make \[ robust on input line 62.
Applying: [2015/01/01] Make \] robust on input line 74.
LaTeX Info: Redefining \] on input line 75.
Already applied: [0000/00/00] Make \] robust on input line 83.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size10.clo
File: size10.clo 2024/02/08 v1.4n Standard LaTeX file (size option)
)
\c@part=\count278
\c@section=\count279
\c@subsection=\count280
\c@subsubsection=\count281
\c@paragraph=\count282
\c@subparagraph=\count283
\c@figure=\count284
\c@table=\count285
\abovecaptionskip=\skip52
\belowcaptionskip=\skip53
\bibindent=\dimen165
)
\bleed=\dimen166
\longtwo@top=\skip54
\longtwo@bottom=\skip55
\longtwo@box=\box64

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/ulem\ulem.sty
\UL@box=\box65
\UL@hyphenbox=\box66
\UL@skip=\skip56
\UL@hook=\toks21
\UL@height=\dimen167
\UL@pe=\count286
\UL@pixel=\dimen168
\ULC@box=\box67
Package: ulem 2019/11/18
\ULdepth=\dimen169
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/upquote\upquote.sty
Package: upquote 2012/04/19 v1.3 upright-quote and grave-accent glyphs in verba
tim
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/regexpatch\regexpat
ch.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3kernel\expl3.sty
Package: expl3 2024-05-27 L3 programming layer (loader) 

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend
-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count287
\l__pdf_internal_box=\box68
))
Package: regexpatch 2021/03/21 v0.2f Extending etoolbox patching commands

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3packages/xparse\x
parse.sty
Package: xparse 2024-05-08 L3 Experimental document command parser
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks22
\inpenc@posthook=\toks23
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/xmltex/passivetex\ucharac
ters.sty
   defining Unicode char U+A0 (decimal 160)
   defining Unicode char U+A1 (decimal 161)
   defining Unicode char U+A2 (decimal 162)
   defining Unicode char U+A3 (decimal 163)
   defining Unicode char U+A4 (decimal 164)
   defining Unicode char U+A5 (decimal 165)
   defining Unicode char U+A6 (decimal 166)
   defining Unicode char U+A7 (decimal 167)
   defining Unicode char U+A8 (decimal 168)
   defining Unicode char U+A9 (decimal 169)
   defining Unicode char U+AA (decimal 170)
   defining Unicode char U+AB (decimal 171)
   defining Unicode char U+AC (decimal 172)
   defining Unicode char U+AD (decimal 173)
   defining Unicode char U+AE (decimal 174)
   defining Unicode char U+AF (decimal 175)
   defining Unicode char U+B0 (decimal 176)
   defining Unicode char U+B1 (decimal 177)
   defining Unicode char U+B2 (decimal 178)
   defining Unicode char U+B3 (decimal 179)
   defining Unicode char U+B4 (decimal 180)
   defining Unicode char U+B5 (decimal 181)
   defining Unicode char U+B6 (decimal 182)
   defining Unicode char U+B7 (decimal 183)
   defining Unicode char U+B8 (decimal 184)
   defining Unicode char U+B9 (decimal 185)
   defining Unicode char U+BA (decimal 186)
   defining Unicode char U+BB (decimal 187)
   defining Unicode char U+BC (decimal 188)
   defining Unicode char U+BD (decimal 189)
   defining Unicode char U+BE (decimal 190)
   defining Unicode char U+BF (decimal 191)
   defining Unicode char U+C0 (decimal 192)
   defining Unicode char U+C1 (decimal 193)
   defining Unicode char U+C2 (decimal 194)
   defining Unicode char U+C3 (decimal 195)
   defining Unicode char U+C4 (decimal 196)
   defining Unicode char U+C5 (decimal 197)
   defining Unicode char U+C6 (decimal 198)
   defining Unicode char U+C7 (decimal 199)
   defining Unicode char U+C8 (decimal 200)
   defining Unicode char U+C9 (decimal 201)
   defining Unicode char U+CA (decimal 202)
   defining Unicode char U+CB (decimal 203)
   defining Unicode char U+CC (decimal 204)
   defining Unicode char U+CD (decimal 205)
   defining Unicode char U+CE (decimal 206)
   defining Unicode char U+CF (decimal 207)
   defining Unicode char U+D0 (decimal 208)
   defining Unicode char U+D1 (decimal 209)
   defining Unicode char U+D2 (decimal 210)
   defining Unicode char U+D3 (decimal 211)
   defining Unicode char U+D4 (decimal 212)
   defining Unicode char U+D5 (decimal 213)
   defining Unicode char U+D6 (decimal 214)
   defining Unicode char U+D7 (decimal 215)
   defining Unicode char U+D8 (decimal 216)
   defining Unicode char U+D9 (decimal 217)
   defining Unicode char U+DA (decimal 218)
   defining Unicode char U+DB (decimal 219)
   defining Unicode char U+DC (decimal 220)
   defining Unicode char U+DD (decimal 221)
   defining Unicode char U+DE (decimal 222)
   defining Unicode char U+DF (decimal 223)
   defining Unicode char U+E0 (decimal 224)
   defining Unicode char U+E1 (decimal 225)
   defining Unicode char U+E2 (decimal 226)
   defining Unicode char U+E3 (decimal 227)
   defining Unicode char U+E4 (decimal 228)
   defining Unicode char U+E5 (decimal 229)
   defining Unicode char U+E6 (decimal 230)
   defining Unicode char U+E7 (decimal 231)
   defining Unicode char U+E8 (decimal 232)
   defining Unicode char U+E9 (decimal 233)
   defining Unicode char U+EA (decimal 234)
   defining Unicode char U+EB (decimal 235)
   defining Unicode char U+EC (decimal 236)
   defining Unicode char U+ED (decimal 237)
   defining Unicode char U+EE (decimal 238)
   defining Unicode char U+EF (decimal 239)
   defining Unicode char U+F0 (decimal 240)
   defining Unicode char U+F1 (decimal 241)
   defining Unicode char U+F2 (decimal 242)
   defining Unicode char U+F3 (decimal 243)
   defining Unicode char U+F4 (decimal 244)
   defining Unicode char U+F5 (decimal 245)
   defining Unicode char U+F6 (decimal 246)
   defining Unicode char U+F7 (decimal 247)
   defining Unicode char U+F8 (decimal 248)
   defining Unicode char U+F9 (decimal 249)
   defining Unicode char U+FA (decimal 250)
   defining Unicode char U+FB (decimal 251)
   defining Unicode char U+FC (decimal 252)
   defining Unicode char U+FD (decimal 253)
   defining Unicode char U+FE (decimal 254)
   defining Unicode char U+FF (decimal 255)
   defining Unicode char U+100 (decimal 256)
   defining Unicode char U+101 (decimal 257)
   defining Unicode char U+102 (decimal 258)
   defining Unicode char U+103 (decimal 259)
   defining Unicode char U+104 (decimal 260)
   defining Unicode char U+105 (decimal 261)
   defining Unicode char U+106 (decimal 262)
   defining Unicode char U+107 (decimal 263)
   defining Unicode char U+108 (decimal 264)
   defining Unicode char U+109 (decimal 265)
   defining Unicode char U+10A (decimal 266)
   defining Unicode char U+10B (decimal 267)
   defining Unicode char U+10C (decimal 268)
   defining Unicode char U+10D (decimal 269)
   defining Unicode char U+10E (decimal 270)
   defining Unicode char U+10F (decimal 271)
   defining Unicode char U+110 (decimal 272)
   defining Unicode char U+111 (decimal 273)
   defining Unicode char U+112 (decimal 274)
   defining Unicode char U+113 (decimal 275)
   defining Unicode char U+114 (decimal 276)
   defining Unicode char U+115 (decimal 277)
   defining Unicode char U+116 (decimal 278)
   defining Unicode char U+117 (decimal 279)
   defining Unicode char U+118 (decimal 280)
   defining Unicode char U+119 (decimal 281)
   defining Unicode char U+11A (decimal 282)
   defining Unicode char U+11B (decimal 283)
   defining Unicode char U+11C (decimal 284)
   defining Unicode char U+11D (decimal 285)
   defining Unicode char U+11E (decimal 286)
   defining Unicode char U+11F (decimal 287)
   defining Unicode char U+120 (decimal 288)
   defining Unicode char U+121 (decimal 289)
   defining Unicode char U+122 (decimal 290)
   defining Unicode char U+123 (decimal 291)
   defining Unicode char U+124 (decimal 292)
   defining Unicode char U+125 (decimal 293)
   defining Unicode char U+126 (decimal 294)
   defining Unicode char U+127 (decimal 295)
   defining Unicode char U+128 (decimal 296)
   defining Unicode char U+129 (decimal 297)
   defining Unicode char U+12A (decimal 298)
   defining Unicode char U+12B (decimal 299)
   defining Unicode char U+12C (decimal 300)
   defining Unicode char U+12D (decimal 301)
   defining Unicode char U+12E (decimal 302)
   defining Unicode char U+12F (decimal 303)
   defining Unicode char U+130 (decimal 304)
   defining Unicode char U+131 (decimal 305)
   defining Unicode char U+132 (decimal 306)
   defining Unicode char U+133 (decimal 307)
   defining Unicode char U+134 (decimal 308)
   defining Unicode char U+135 (decimal 309)
   defining Unicode char U+136 (decimal 310)
   defining Unicode char U+137 (decimal 311)
   defining Unicode char U+138 (decimal 312)
   defining Unicode char U+139 (decimal 313)
   defining Unicode char U+13A (decimal 314)
   defining Unicode char U+13B (decimal 315)
   defining Unicode char U+13C (decimal 316)
   defining Unicode char U+13D (decimal 317)
   defining Unicode char U+13E (decimal 318)
   defining Unicode char U+13F (decimal 319)
   defining Unicode char U+140 (decimal 320)
   defining Unicode char U+141 (decimal 321)
   defining Unicode char U+142 (decimal 322)
   defining Unicode char U+143 (decimal 323)
   defining Unicode char U+144 (decimal 324)
   defining Unicode char U+145 (decimal 325)
   defining Unicode char U+146 (decimal 326)
   defining Unicode char U+147 (decimal 327)
   defining Unicode char U+148 (decimal 328)
   defining Unicode char U+149 (decimal 329)
   defining Unicode char U+14A (decimal 330)
   defining Unicode char U+14B (decimal 331)
   defining Unicode char U+14C (decimal 332)
   defining Unicode char U+14D (decimal 333)
   defining Unicode char U+14E (decimal 334)
   defining Unicode char U+14F (decimal 335)
   defining Unicode char U+150 (decimal 336)
   defining Unicode char U+151 (decimal 337)
   defining Unicode char U+152 (decimal 338)
   defining Unicode char U+153 (decimal 339)
   defining Unicode char U+154 (decimal 340)
   defining Unicode char U+155 (decimal 341)
   defining Unicode char U+156 (decimal 342)
   defining Unicode char U+157 (decimal 343)
   defining Unicode char U+158 (decimal 344)
   defining Unicode char U+159 (decimal 345)
   defining Unicode char U+15A (decimal 346)
   defining Unicode char U+15B (decimal 347)
   defining Unicode char U+15C (decimal 348)
   defining Unicode char U+15D (decimal 349)
   defining Unicode char U+15E (decimal 350)
   defining Unicode char U+15F (decimal 351)
   defining Unicode char U+160 (decimal 352)
   defining Unicode char U+161 (decimal 353)
   defining Unicode char U+162 (decimal 354)
   defining Unicode char U+163 (decimal 355)
   defining Unicode char U+164 (decimal 356)
   defining Unicode char U+165 (decimal 357)
   defining Unicode char U+166 (decimal 358)
   defining Unicode char U+167 (decimal 359)
   defining Unicode char U+168 (decimal 360)
   defining Unicode char U+169 (decimal 361)
   defining Unicode char U+16A (decimal 362)
   defining Unicode char U+16B (decimal 363)
   defining Unicode char U+16C (decimal 364)
   defining Unicode char U+16D (decimal 365)
   defining Unicode char U+16E (decimal 366)
   defining Unicode char U+16F (decimal 367)
   defining Unicode char U+170 (decimal 368)
   defining Unicode char U+171 (decimal 369)
   defining Unicode char U+172 (decimal 370)
   defining Unicode char U+173 (decimal 371)
   defining Unicode char U+174 (decimal 372)
   defining Unicode char U+175 (decimal 373)
   defining Unicode char U+176 (decimal 374)
   defining Unicode char U+177 (decimal 375)
   defining Unicode char U+178 (decimal 376)
   defining Unicode char U+179 (decimal 377)
   defining Unicode char U+17A (decimal 378)
   defining Unicode char U+17B (decimal 379)
   defining Unicode char U+17C (decimal 380)
   defining Unicode char U+17D (decimal 381)
   defining Unicode char U+17E (decimal 382)
   defining Unicode char U+192 (decimal 402)
   defining Unicode char U+195 (decimal 405)
   defining Unicode char U+19E (decimal 414)
   defining Unicode char U+1AA (decimal 426)
   defining Unicode char U+1BA (decimal 442)
   defining Unicode char U+1C2 (decimal 450)
   defining Unicode char U+1F5 (decimal 501)
   defining Unicode char U+250 (decimal 592)
   defining Unicode char U+252 (decimal 594)
   defining Unicode char U+254 (decimal 596)
   defining Unicode char U+256 (decimal 598)
   defining Unicode char U+258 (decimal 600)
   defining Unicode char U+259 (decimal 601)
   defining Unicode char U+25B (decimal 603)
   defining Unicode char U+261 (decimal 609)
   defining Unicode char U+263 (decimal 611)
   defining Unicode char U+264 (decimal 612)
   defining Unicode char U+265 (decimal 613)
   defining Unicode char U+26C (decimal 620)
   defining Unicode char U+26D (decimal 621)
   defining Unicode char U+26F (decimal 623)
   defining Unicode char U+270 (decimal 624)
   defining Unicode char U+271 (decimal 625)
   defining Unicode char U+272 (decimal 626)
   defining Unicode char U+273 (decimal 627)
   defining Unicode char U+277 (decimal 631)
   defining Unicode char U+278 (decimal 632)
   defining Unicode char U+279 (decimal 633)
   defining Unicode char U+27A (decimal 634)
   defining Unicode char U+27B (decimal 635)
   defining Unicode char U+27C (decimal 636)
   defining Unicode char U+27D (decimal 637)
   defining Unicode char U+27E (decimal 638)
   defining Unicode char U+27F (decimal 639)
   defining Unicode char U+282 (decimal 642)
   defining Unicode char U+283 (decimal 643)
   defining Unicode char U+287 (decimal 647)
   defining Unicode char U+288 (decimal 648)
   defining Unicode char U+28A (decimal 650)
   defining Unicode char U+28B (decimal 651)
   defining Unicode char U+28C (decimal 652)
   defining Unicode char U+28D (decimal 653)
   defining Unicode char U+28E (decimal 654)
   defining Unicode char U+290 (decimal 656)
   defining Unicode char U+292 (decimal 658)
   defining Unicode char U+294 (decimal 660)
   defining Unicode char U+295 (decimal 661)
   defining Unicode char U+296 (decimal 662)
   defining Unicode char U+29E (decimal 670)
   defining Unicode char U+2A4 (decimal 676)
   defining Unicode char U+2A7 (decimal 679)
   defining Unicode char U+2BC (decimal 700)
   defining Unicode char U+2C7 (decimal 711)
   defining Unicode char U+2C8 (decimal 712)
   defining Unicode char U+2CC (decimal 716)
   defining Unicode char U+2D0 (decimal 720)
   defining Unicode char U+2D1 (decimal 721)
   defining Unicode char U+2D2 (decimal 722)
   defining Unicode char U+2D3 (decimal 723)
   defining Unicode char U+2D4 (decimal 724)
   defining Unicode char U+2D5 (decimal 725)
   defining Unicode char U+2D8 (decimal 728)
   defining Unicode char U+2D9 (decimal 729)
   defining Unicode char U+2DA (decimal 730)
   defining Unicode char U+2DB (decimal 731)
   defining Unicode char U+2DC (decimal 732)
   defining Unicode char U+2DD (decimal 733)
   defining Unicode char U+2E5 (decimal 741)
   defining Unicode char U+2E6 (decimal 742)
   defining Unicode char U+2E7 (decimal 743)
   defining Unicode char U+2E8 (decimal 744)
   defining Unicode char U+2E9 (decimal 745)
   defining Unicode char U+300 (decimal 768)
   defining Unicode char U+301 (decimal 769)
   defining Unicode char U+302 (decimal 770)
   defining Unicode char U+303 (decimal 771)
   defining Unicode char U+304 (decimal 772)
   defining Unicode char U+306 (decimal 774)
   defining Unicode char U+307 (decimal 775)
   defining Unicode char U+308 (decimal 776)
   defining Unicode char U+30A (decimal 778)
   defining Unicode char U+30B (decimal 779)
   defining Unicode char U+30C (decimal 780)
   defining Unicode char U+30F (decimal 783)
   defining Unicode char U+311 (decimal 785)
   defining Unicode char U+318 (decimal 792)
   defining Unicode char U+319 (decimal 793)
   defining Unicode char U+321 (decimal 801)
   defining Unicode char U+322 (decimal 802)
   defining Unicode char U+327 (decimal 807)
   defining Unicode char U+328 (decimal 808)
   defining Unicode char U+32A (decimal 810)
   defining Unicode char U+32B (decimal 811)
   defining Unicode char U+32F (decimal 815)
   defining Unicode char U+335 (decimal 821)
   defining Unicode char U+336 (decimal 822)
   defining Unicode char U+337 (decimal 823)
   defining Unicode char U+338 (decimal 824)
   defining Unicode char U+33A (decimal 826)
   defining Unicode char U+33B (decimal 827)
   defining Unicode char U+33C (decimal 828)
   defining Unicode char U+33D (decimal 829)
   defining Unicode char U+361 (decimal 865)
   defining Unicode char U+386 (decimal 902)
   defining Unicode char U+388 (decimal 904)
   defining Unicode char U+389 (decimal 905)
   defining Unicode char U+38A (decimal 906)
   defining Unicode char U+38C (decimal 908)
   defining Unicode char U+38E (decimal 910)
   defining Unicode char U+38F (decimal 911)
   defining Unicode char U+390 (decimal 912)
   defining Unicode char U+391 (decimal 913)
   defining Unicode char U+392 (decimal 914)
   defining Unicode char U+393 (decimal 915)
   defining Unicode char U+394 (decimal 916)
   defining Unicode char U+395 (decimal 917)
   defining Unicode char U+396 (decimal 918)
   defining Unicode char U+397 (decimal 919)
   defining Unicode char U+398 (decimal 920)
   defining Unicode char U+399 (decimal 921)
   defining Unicode char U+39A (decimal 922)
   defining Unicode char U+39B (decimal 923)
   defining Unicode char U+39C (decimal 924)
   defining Unicode char U+39D (decimal 925)
   defining Unicode char U+39E (decimal 926)
   defining Unicode char U+39F (decimal 927)
   defining Unicode char U+3A0 (decimal 928)
   defining Unicode char U+3A1 (decimal 929)
   defining Unicode char U+3A3 (decimal 931)
   defining Unicode char U+3A4 (decimal 932)
   defining Unicode char U+3A5 (decimal 933)
   defining Unicode char U+3A6 (decimal 934)
   defining Unicode char U+3A7 (decimal 935)
   defining Unicode char U+3A8 (decimal 936)
   defining Unicode char U+3A9 (decimal 937)
   defining Unicode char U+3AA (decimal 938)
   defining Unicode char U+3AB (decimal 939)
   defining Unicode char U+3AC (decimal 940)
   defining Unicode char U+3AD (decimal 941)
   defining Unicode char U+3AE (decimal 942)
   defining Unicode char U+3AF (decimal 943)
   defining Unicode char U+3B0 (decimal 944)
   defining Unicode char U+3B1 (decimal 945)
   defining Unicode char U+3B2 (decimal 946)
   defining Unicode char U+3B3 (decimal 947)
   defining Unicode char U+3B4 (decimal 948)
   defining Unicode char U+3B5 (decimal 949)
   defining Unicode char U+3B6 (decimal 950)
   defining Unicode char U+3B7 (decimal 951)
   defining Unicode char U+3B8 (decimal 952)
   defining Unicode char U+3B9 (decimal 953)
   defining Unicode char U+3BA (decimal 954)
   defining Unicode char U+3BB (decimal 955)
   defining Unicode char U+3BC (decimal 956)
   defining Unicode char U+3BD (decimal 957)
   defining Unicode char U+3BE (decimal 958)
   defining Unicode char U+3BF (decimal 959)
   defining Unicode char U+3C0 (decimal 960)
   defining Unicode char U+3C1 (decimal 961)
   defining Unicode char U+3C2 (decimal 962)
   defining Unicode char U+3C3 (decimal 963)
   defining Unicode char U+3C4 (decimal 964)
   defining Unicode char U+3C5 (decimal 965)
   defining Unicode char U+3C6 (decimal 966)
   defining Unicode char U+3C7 (decimal 967)
   defining Unicode char U+3C8 (decimal 968)
   defining Unicode char U+3C9 (decimal 969)
   defining Unicode char U+3CA (decimal 970)
   defining Unicode char U+3CB (decimal 971)
   defining Unicode char U+3CC (decimal 972)
   defining Unicode char U+3CD (decimal 973)
   defining Unicode char U+3CE (decimal 974)
   defining Unicode char U+3D0 (decimal 976)
   defining Unicode char U+3D1 (decimal 977)
   defining Unicode char U+3D2 (decimal 978)
   defining Unicode char U+3D5 (decimal 981)
   defining Unicode char U+3D6 (decimal 982)
   defining Unicode char U+3DC (decimal 988)
   defining Unicode char U+3F0 (decimal 1008)
   defining Unicode char U+3F1 (decimal 1009)
   defining Unicode char U+401 (decimal 1025)
   defining Unicode char U+402 (decimal 1026)
   defining Unicode char U+403 (decimal 1027)
   defining Unicode char U+404 (decimal 1028)
   defining Unicode char U+405 (decimal 1029)
   defining Unicode char U+406 (decimal 1030)
   defining Unicode char U+407 (decimal 1031)
   defining Unicode char U+408 (decimal 1032)
   defining Unicode char U+409 (decimal 1033)
   defining Unicode char U+40A (decimal 1034)
   defining Unicode char U+40B (decimal 1035)
   defining Unicode char U+40C (decimal 1036)
   defining Unicode char U+40E (decimal 1038)
   defining Unicode char U+40F (decimal 1039)
   defining Unicode char U+410 (decimal 1040)
   defining Unicode char U+411 (decimal 1041)
   defining Unicode char U+412 (decimal 1042)
   defining Unicode char U+413 (decimal 1043)
   defining Unicode char U+414 (decimal 1044)
   defining Unicode char U+415 (decimal 1045)
   defining Unicode char U+416 (decimal 1046)
   defining Unicode char U+417 (decimal 1047)
   defining Unicode char U+418 (decimal 1048)
   defining Unicode char U+419 (decimal 1049)
   defining Unicode char U+41A (decimal 1050)
   defining Unicode char U+41B (decimal 1051)
   defining Unicode char U+41C (decimal 1052)
   defining Unicode char U+41D (decimal 1053)
   defining Unicode char U+41E (decimal 1054)
   defining Unicode char U+41F (decimal 1055)
   defining Unicode char U+420 (decimal 1056)
   defining Unicode char U+421 (decimal 1057)
   defining Unicode char U+422 (decimal 1058)
   defining Unicode char U+423 (decimal 1059)
   defining Unicode char U+424 (decimal 1060)
   defining Unicode char U+425 (decimal 1061)
   defining Unicode char U+426 (decimal 1062)
   defining Unicode char U+427 (decimal 1063)
   defining Unicode char U+428 (decimal 1064)
   defining Unicode char U+429 (decimal 1065)
   defining Unicode char U+42A (decimal 1066)
   defining Unicode char U+42B (decimal 1067)
   defining Unicode char U+42C (decimal 1068)
   defining Unicode char U+42D (decimal 1069)
   defining Unicode char U+42E (decimal 1070)
   defining Unicode char U+42F (decimal 1071)
   defining Unicode char U+430 (decimal 1072)
   defining Unicode char U+431 (decimal 1073)
   defining Unicode char U+432 (decimal 1074)
   defining Unicode char U+433 (decimal 1075)
   defining Unicode char U+434 (decimal 1076)
   defining Unicode char U+435 (decimal 1077)
   defining Unicode char U+436 (decimal 1078)
   defining Unicode char U+437 (decimal 1079)
   defining Unicode char U+438 (decimal 1080)
   defining Unicode char U+439 (decimal 1081)
   defining Unicode char U+43A (decimal 1082)
   defining Unicode char U+43B (decimal 1083)
   defining Unicode char U+43C (decimal 1084)
   defining Unicode char U+43D (decimal 1085)
   defining Unicode char U+43E (decimal 1086)
   defining Unicode char U+43F (decimal 1087)
   defining Unicode char U+440 (decimal 1088)
   defining Unicode char U+441 (decimal 1089)
   defining Unicode char U+442 (decimal 1090)
   defining Unicode char U+443 (decimal 1091)
   defining Unicode char U+444 (decimal 1092)
   defining Unicode char U+445 (decimal 1093)
   defining Unicode char U+446 (decimal 1094)
   defining Unicode char U+447 (decimal 1095)
   defining Unicode char U+448 (decimal 1096)
   defining Unicode char U+449 (decimal 1097)
   defining Unicode char U+44A (decimal 1098)
   defining Unicode char U+44B (decimal 1099)
   defining Unicode char U+44C (decimal 1100)
   defining Unicode char U+44D (decimal 1101)
   defining Unicode char U+44E (decimal 1102)
   defining Unicode char U+44F (decimal 1103)
   defining Unicode char U+451 (decimal 1105)
   defining Unicode char U+452 (decimal 1106)
   defining Unicode char U+453 (decimal 1107)
   defining Unicode char U+454 (decimal 1108)
   defining Unicode char U+455 (decimal 1109)
   defining Unicode char U+456 (decimal 1110)
   defining Unicode char U+457 (decimal 1111)
   defining Unicode char U+458 (decimal 1112)
   defining Unicode char U+459 (decimal 1113)
   defining Unicode char U+45A (decimal 1114)
   defining Unicode char U+45B (decimal 1115)
   defining Unicode char U+45C (decimal 1116)
   defining Unicode char U+45E (decimal 1118)
   defining Unicode char U+45F (decimal 1119)
   defining Unicode char U+460 (decimal 1120)
   defining Unicode char U+461 (decimal 1121)
   defining Unicode char U+462 (decimal 1122)
   defining Unicode char U+464 (decimal 1124)
   defining Unicode char U+465 (decimal 1125)
   defining Unicode char U+466 (decimal 1126)
   defining Unicode char U+467 (decimal 1127)
   defining Unicode char U+468 (decimal 1128)
   defining Unicode char U+469 (decimal 1129)
   defining Unicode char U+46A (decimal 1130)
   defining Unicode char U+46C (decimal 1132)
   defining Unicode char U+46D (decimal 1133)
   defining Unicode char U+46E (decimal 1134)
   defining Unicode char U+46F (decimal 1135)
   defining Unicode char U+470 (decimal 1136)
   defining Unicode char U+471 (decimal 1137)
   defining Unicode char U+472 (decimal 1138)
   defining Unicode char U+474 (decimal 1140)
   defining Unicode char U+478 (decimal 1144)
   defining Unicode char U+479 (decimal 1145)
   defining Unicode char U+47A (decimal 1146)
   defining Unicode char U+47B (decimal 1147)
   defining Unicode char U+47C (decimal 1148)
   defining Unicode char U+47D (decimal 1149)
   defining Unicode char U+47E (decimal 1150)
   defining Unicode char U+47F (decimal 1151)
   defining Unicode char U+480 (decimal 1152)
   defining Unicode char U+481 (decimal 1153)
   defining Unicode char U+482 (decimal 1154)
   defining Unicode char U+488 (decimal 1160)
   defining Unicode char U+489 (decimal 1161)
   defining Unicode char U+48C (decimal 1164)
   defining Unicode char U+48D (decimal 1165)
   defining Unicode char U+48E (decimal 1166)
   defining Unicode char U+48F (decimal 1167)
   defining Unicode char U+490 (decimal 1168)
   defining Unicode char U+491 (decimal 1169)
   defining Unicode char U+492 (decimal 1170)
   defining Unicode char U+493 (decimal 1171)
   defining Unicode char U+494 (decimal 1172)
   defining Unicode char U+495 (decimal 1173)
   defining Unicode char U+496 (decimal 1174)
   defining Unicode char U+497 (decimal 1175)
   defining Unicode char U+498 (decimal 1176)
   defining Unicode char U+499 (decimal 1177)
   defining Unicode char U+49A (decimal 1178)
   defining Unicode char U+49B (decimal 1179)
   defining Unicode char U+49C (decimal 1180)
   defining Unicode char U+49D (decimal 1181)
   defining Unicode char U+49E (decimal 1182)
   defining Unicode char U+49F (decimal 1183)
   defining Unicode char U+4A0 (decimal 1184)
   defining Unicode char U+4A1 (decimal 1185)
   defining Unicode char U+4A2 (decimal 1186)
   defining Unicode char U+4A3 (decimal 1187)
   defining Unicode char U+4A4 (decimal 1188)
   defining Unicode char U+4A5 (decimal 1189)
   defining Unicode char U+4A6 (decimal 1190)
   defining Unicode char U+4A7 (decimal 1191)
   defining Unicode char U+4A8 (decimal 1192)
   defining Unicode char U+4A9 (decimal 1193)
   defining Unicode char U+4AA (decimal 1194)
   defining Unicode char U+4AB (decimal 1195)
   defining Unicode char U+4AC (decimal 1196)
   defining Unicode char U+4AD (decimal 1197)
   defining Unicode char U+4AE (decimal 1198)
   defining Unicode char U+4AF (decimal 1199)
   defining Unicode char U+4B0 (decimal 1200)
   defining Unicode char U+4B1 (decimal 1201)
   defining Unicode char U+4B2 (decimal 1202)
   defining Unicode char U+4B3 (decimal 1203)
   defining Unicode char U+4B4 (decimal 1204)
   defining Unicode char U+4B5 (decimal 1205)
   defining Unicode char U+4B6 (decimal 1206)
   defining Unicode char U+4B7 (decimal 1207)
   defining Unicode char U+4B8 (decimal 1208)
   defining Unicode char U+4B9 (decimal 1209)
   defining Unicode char U+4BA (decimal 1210)
   defining Unicode char U+4BB (decimal 1211)
   defining Unicode char U+4BC (decimal 1212)
   defining Unicode char U+4BD (decimal 1213)
   defining Unicode char U+4BE (decimal 1214)
   defining Unicode char U+4BF (decimal 1215)
   defining Unicode char U+4C0 (decimal 1216)
   defining Unicode char U+4C3 (decimal 1219)
   defining Unicode char U+4C4 (decimal 1220)
   defining Unicode char U+4C7 (decimal 1223)
   defining Unicode char U+4C8 (decimal 1224)
   defining Unicode char U+4CB (decimal 1227)
   defining Unicode char U+4CC (decimal 1228)
   defining Unicode char U+4D4 (decimal 1236)
   defining Unicode char U+4D5 (decimal 1237)
   defining Unicode char U+4D8 (decimal 1240)
   defining Unicode char U+4D9 (decimal 1241)
   defining Unicode char U+4E0 (decimal 1248)
   defining Unicode char U+4E1 (decimal 1249)
   defining Unicode char U+4E8 (decimal 1256)
   defining Unicode char U+4E9 (decimal 1257)
   defining Unicode char U+2002 (decimal 8194)
   defining Unicode char U+2003 (decimal 8195)
   defining Unicode char U+2004 (decimal 8196)
   defining Unicode char U+2005 (decimal 8197)
   defining Unicode char U+2006 (decimal 8198)
   defining Unicode char U+2007 (decimal 8199)
   defining Unicode char U+2008 (decimal 8200)
   defining Unicode char U+2009 (decimal 8201)
   defining Unicode char U+200A (decimal 8202)
   defining Unicode char U+2010 (decimal 8208)
   defining Unicode char U+2013 (decimal 8211)
   defining Unicode char U+2014 (decimal 8212)
   defining Unicode char U+2015 (decimal 8213)
   defining Unicode char U+2016 (decimal 8214)
   defining Unicode char U+2018 (decimal 8216)
   defining Unicode char U+2019 (decimal 8217)
   defining Unicode char U+201A (decimal 8218)
   defining Unicode char U+201B (decimal 8219)
   defining Unicode char U+201C (decimal 8220)
   defining Unicode char U+201D (decimal 8221)
   defining Unicode char U+201E (decimal 8222)
   defining Unicode char U+2020 (decimal 8224)
   defining Unicode char U+2021 (decimal 8225)
   defining Unicode char U+2022 (decimal 8226)
   defining Unicode char U+2024 (decimal 8228)
   defining Unicode char U+2025 (decimal 8229)
   defining Unicode char U+2026 (decimal 8230)
   defining Unicode char U+2030 (decimal 8240)
   defining Unicode char U+2031 (decimal 8241)
   defining Unicode char U+2032 (decimal 8242)
   defining Unicode char U+2033 (decimal 8243)
   defining Unicode char U+2034 (decimal 8244)
   defining Unicode char U+2035 (decimal 8245)
   defining Unicode char U+2039 (decimal 8249)
   defining Unicode char U+203A (decimal 8250)
   defining Unicode char U+20A7 (decimal 8359)
   defining Unicode char U+20AC (decimal 8364)
   defining Unicode char U+20DB (decimal 8411)
   defining Unicode char U+20DC (decimal 8412)
   defining Unicode char U+2102 (decimal 8450)
   defining Unicode char U+210A (decimal 8458)
   defining Unicode char U+210B (decimal 8459)
   defining Unicode char U+210C (decimal 8460)
   defining Unicode char U+210D (decimal 8461)
   defining Unicode char U+210F (decimal 8463)
   defining Unicode char U+2110 (decimal 8464)
   defining Unicode char U+2111 (decimal 8465)
   defining Unicode char U+2112 (decimal 8466)
   defining Unicode char U+2113 (decimal 8467)
   defining Unicode char U+2115 (decimal 8469)
   defining Unicode char U+2116 (decimal 8470)
   defining Unicode char U+2118 (decimal 8472)
   defining Unicode char U+2119 (decimal 8473)
   defining Unicode char U+211A (decimal 8474)
   defining Unicode char U+211B (decimal 8475)
   defining Unicode char U+211C (decimal 8476)
   defining Unicode char U+211D (decimal 8477)
   defining Unicode char U+211E (decimal 8478)
   defining Unicode char U+2122 (decimal 8482)
   defining Unicode char U+2124 (decimal 8484)
   defining Unicode char U+2126 (decimal 8486)
   defining Unicode char U+2127 (decimal 8487)
   defining Unicode char U+2128 (decimal 8488)
   defining Unicode char U+2129 (decimal 8489)
   defining Unicode char U+212B (decimal 8491)
   defining Unicode char U+212C (decimal 8492)
   defining Unicode char U+212D (decimal 8493)
   defining Unicode char U+212F (decimal 8495)
   defining Unicode char U+2130 (decimal 8496)
   defining Unicode char U+2131 (decimal 8497)
   defining Unicode char U+2133 (decimal 8499)
   defining Unicode char U+2134 (decimal 8500)
   defining Unicode char U+2135 (decimal 8501)
   defining Unicode char U+2136 (decimal 8502)
   defining Unicode char U+2137 (decimal 8503)
   defining Unicode char U+2138 (decimal 8504)
   defining Unicode char U+2153 (decimal 8531)
   defining Unicode char U+2154 (decimal 8532)
   defining Unicode char U+2155 (decimal 8533)
   defining Unicode char U+2156 (decimal 8534)
   defining Unicode char U+2157 (decimal 8535)
   defining Unicode char U+2158 (decimal 8536)
   defining Unicode char U+2159 (decimal 8537)
   defining Unicode char U+215A (decimal 8538)
   defining Unicode char U+215B (decimal 8539)
   defining Unicode char U+215C (decimal 8540)
   defining Unicode char U+215D (decimal 8541)
   defining Unicode char U+215E (decimal 8542)
   defining Unicode char U+2190 (decimal 8592)
   defining Unicode char U+2190 (decimal 8592)
   defining Unicode char U+2191 (decimal 8593)
   defining Unicode char U+2192 (decimal 8594)
   defining Unicode char U+2192 (decimal 8594)
   defining Unicode char U+2193 (decimal 8595)
   defining Unicode char U+2194 (decimal 8596)
   defining Unicode char U+2194 (decimal 8596)
   defining Unicode char U+2195 (decimal 8597)
   defining Unicode char U+2196 (decimal 8598)
   defining Unicode char U+2197 (decimal 8599)
   defining Unicode char U+2198 (decimal 8600)
   defining Unicode char U+2199 (decimal 8601)
   defining Unicode char U+219A (decimal 8602)
   defining Unicode char U+219B (decimal 8603)
   defining Unicode char U+219C (decimal 8604)
   defining Unicode char U+219D (decimal 8605)
   defining Unicode char U+219E (decimal 8606)
   defining Unicode char U+21A0 (decimal 8608)
   defining Unicode char U+21A2 (decimal 8610)
   defining Unicode char U+21A3 (decimal 8611)
   defining Unicode char U+21A6 (decimal 8614)
   defining Unicode char U+21A6 (decimal 8614)
   defining Unicode char U+21A9 (decimal 8617)
   defining Unicode char U+21AA (decimal 8618)
   defining Unicode char U+21AB (decimal 8619)
   defining Unicode char U+21AC (decimal 8620)
   defining Unicode char U+21AD (decimal 8621)
   defining Unicode char U+21AE (decimal 8622)
   defining Unicode char U+21B0 (decimal 8624)
   defining Unicode char U+21B1 (decimal 8625)
   defining Unicode char U+21B3 (decimal 8627)
   defining Unicode char U+21B6 (decimal 8630)
   defining Unicode char U+21B7 (decimal 8631)
   defining Unicode char U+21BA (decimal 8634)
   defining Unicode char U+21BB (decimal 8635)
   defining Unicode char U+21BC (decimal 8636)
   defining Unicode char U+21BD (decimal 8637)
   defining Unicode char U+21BE (decimal 8638)
   defining Unicode char U+21BF (decimal 8639)
   defining Unicode char U+21C0 (decimal 8640)
   defining Unicode char U+21C1 (decimal 8641)
   defining Unicode char U+21C2 (decimal 8642)
   defining Unicode char U+21C3 (decimal 8643)
   defining Unicode char U+21C4 (decimal 8644)
   defining Unicode char U+21C5 (decimal 8645)
   defining Unicode char U+21C6 (decimal 8646)
   defining Unicode char U+21C7 (decimal 8647)
   defining Unicode char U+21C8 (decimal 8648)
   defining Unicode char U+21C9 (decimal 8649)
   defining Unicode char U+21CA (decimal 8650)
   defining Unicode char U+21CB (decimal 8651)
   defining Unicode char U+21CC (decimal 8652)
   defining Unicode char U+21CD (decimal 8653)
   defining Unicode char U+21CE (decimal 8654)
   defining Unicode char U+21CF (decimal 8655)
   defining Unicode char U+21D0 (decimal 8656)
   defining Unicode char U+21D0 (decimal 8656)
   defining Unicode char U+21D1 (decimal 8657)
   defining Unicode char U+21D2 (decimal 8658)
   defining Unicode char U+21D2 (decimal 8658)
   defining Unicode char U+21D3 (decimal 8659)
   defining Unicode char U+21D4 (decimal 8660)
   defining Unicode char U+21D4 (decimal 8660)
   defining Unicode char U+21D5 (decimal 8661)
   defining Unicode char U+21DA (decimal 8666)
   defining Unicode char U+21DB (decimal 8667)
   defining Unicode char U+21DD (decimal 8669)
   defining Unicode char U+21F5 (decimal 8693)
   defining Unicode char U+2200 (decimal 8704)
   defining Unicode char U+2201 (decimal 8705)
   defining Unicode char U+2202 (decimal 8706)
   defining Unicode char U+2203 (decimal 8707)
   defining Unicode char U+2204 (decimal 8708)
   defining Unicode char U+2205 (decimal 8709)
   defining Unicode char U+2207 (decimal 8711)
   defining Unicode char U+2208 (decimal 8712)
   defining Unicode char U+2209 (decimal 8713)
   defining Unicode char U+220B (decimal 8715)
   defining Unicode char U+220C (decimal 8716)
   defining Unicode char U+220F (decimal 8719)
   defining Unicode char U+2210 (decimal 8720)
   defining Unicode char U+2211 (decimal 8721)
   defining Unicode char U+2212 (decimal 8722)
   defining Unicode char U+2213 (decimal 8723)
   defining Unicode char U+2214 (decimal 8724)
   defining Unicode char U+2216 (decimal 8726)
   defining Unicode char U+2217 (decimal 8727)
   defining Unicode char U+2218 (decimal 8728)
   defining Unicode char U+2219 (decimal 8729)
   defining Unicode char U+221A (decimal 8730)
   defining Unicode char U+221D (decimal 8733)
   defining Unicode char U+221E (decimal 8734)
   defining Unicode char U+221F (decimal 8735)
   defining Unicode char U+2220 (decimal 8736)
   defining Unicode char U+2221 (decimal 8737)
   defining Unicode char U+2222 (decimal 8738)
   defining Unicode char U+2223 (decimal 8739)
   defining Unicode char U+2224 (decimal 8740)
   defining Unicode char U+2225 (decimal 8741)
   defining Unicode char U+2226 (decimal 8742)
   defining Unicode char U+2227 (decimal 8743)
   defining Unicode char U+2228 (decimal 8744)
   defining Unicode char U+2229 (decimal 8745)
   defining Unicode char U+222A (decimal 8746)
   defining Unicode char U+222B (decimal 8747)
   defining Unicode char U+222C (decimal 8748)
   defining Unicode char U+222D (decimal 8749)
   defining Unicode char U+222E (decimal 8750)
   defining Unicode char U+222F (decimal 8751)
   defining Unicode char U+2230 (decimal 8752)
   defining Unicode char U+2231 (decimal 8753)
   defining Unicode char U+2232 (decimal 8754)
   defining Unicode char U+2233 (decimal 8755)
   defining Unicode char U+2234 (decimal 8756)
   defining Unicode char U+2235 (decimal 8757)
   defining Unicode char U+2237 (decimal 8759)
   defining Unicode char U+2238 (decimal 8760)
   defining Unicode char U+223A (decimal 8762)
   defining Unicode char U+223B (decimal 8763)
   defining Unicode char U+223C (decimal 8764)
   defining Unicode char U+223D (decimal 8765)
   defining Unicode char U+223E (decimal 8766)
   defining Unicode char U+2240 (decimal 8768)
   defining Unicode char U+2241 (decimal 8769)
   defining Unicode char U+2242 (decimal 8770)
   defining Unicode char U+2243 (decimal 8771)
   defining Unicode char U+2244 (decimal 8772)
   defining Unicode char U+2245 (decimal 8773)
   defining Unicode char U+2246 (decimal 8774)
   defining Unicode char U+2247 (decimal 8775)
   defining Unicode char U+2248 (decimal 8776)
   defining Unicode char U+2249 (decimal 8777)
   defining Unicode char U+224A (decimal 8778)
   defining Unicode char U+224B (decimal 8779)
   defining Unicode char U+224C (decimal 8780)
   defining Unicode char U+224D (decimal 8781)
   defining Unicode char U+224E (decimal 8782)
   defining Unicode char U+224F (decimal 8783)
   defining Unicode char U+2250 (decimal 8784)
   defining Unicode char U+2251 (decimal 8785)
   defining Unicode char U+2252 (decimal 8786)
   defining Unicode char U+2253 (decimal 8787)
   defining Unicode char U+2254 (decimal 8788)
   defining Unicode char U+2255 (decimal 8789)
   defining Unicode char U+2256 (decimal 8790)
   defining Unicode char U+2257 (decimal 8791)
   defining Unicode char U+2259 (decimal 8793)
   defining Unicode char U+225A (decimal 8794)
   defining Unicode char U+225B (decimal 8795)
   defining Unicode char U+225C (decimal 8796)
   defining Unicode char U+225F (decimal 8799)
   defining Unicode char U+2260 (decimal 8800)
   defining Unicode char U+2261 (decimal 8801)
   defining Unicode char U+2262 (decimal 8802)
   defining Unicode char U+2264 (decimal 8804)
   defining Unicode char U+2265 (decimal 8805)
   defining Unicode char U+2266 (decimal 8806)
   defining Unicode char U+2267 (decimal 8807)
   defining Unicode char U+2268 (decimal 8808)
   defining Unicode char U+2269 (decimal 8809)
   defining Unicode char U+226A (decimal 8810)
   defining Unicode char U+226B (decimal 8811)
   defining Unicode char U+226C (decimal 8812)
   defining Unicode char U+226D (decimal 8813)
   defining Unicode char U+226E (decimal 8814)
   defining Unicode char U+226F (decimal 8815)
   defining Unicode char U+2270 (decimal 8816)
   defining Unicode char U+2271 (decimal 8817)
   defining Unicode char U+2272 (decimal 8818)
   defining Unicode char U+2273 (decimal 8819)
   defining Unicode char U+2274 (decimal 8820)
   defining Unicode char U+2275 (decimal 8821)
   defining Unicode char U+2276 (decimal 8822)
   defining Unicode char U+2277 (decimal 8823)
   defining Unicode char U+2278 (decimal 8824)
   defining Unicode char U+2279 (decimal 8825)
   defining Unicode char U+227A (decimal 8826)
   defining Unicode char U+227B (decimal 8827)
   defining Unicode char U+227C (decimal 8828)
   defining Unicode char U+227D (decimal 8829)
   defining Unicode char U+227E (decimal 8830)
   defining Unicode char U+227F (decimal 8831)
   defining Unicode char U+2280 (decimal 8832)
   defining Unicode char U+2281 (decimal 8833)
   defining Unicode char U+2282 (decimal 8834)
   defining Unicode char U+2283 (decimal 8835)
   defining Unicode char U+2284 (decimal 8836)
   defining Unicode char U+2285 (decimal 8837)
   defining Unicode char U+2286 (decimal 8838)
   defining Unicode char U+2287 (decimal 8839)
   defining Unicode char U+2288 (decimal 8840)
   defining Unicode char U+2289 (decimal 8841)
   defining Unicode char U+228A (decimal 8842)
   defining Unicode char U+228B (decimal 8843)
   defining Unicode char U+228E (decimal 8846)
   defining Unicode char U+228F (decimal 8847)
   defining Unicode char U+2290 (decimal 8848)
   defining Unicode char U+2291 (decimal 8849)
   defining Unicode char U+2292 (decimal 8850)
   defining Unicode char U+2293 (decimal 8851)
   defining Unicode char U+2294 (decimal 8852)
   defining Unicode char U+2295 (decimal 8853)
   defining Unicode char U+2296 (decimal 8854)
   defining Unicode char U+2297 (decimal 8855)
   defining Unicode char U+2298 (decimal 8856)
   defining Unicode char U+2299 (decimal 8857)
   defining Unicode char U+229A (decimal 8858)
   defining Unicode char U+229B (decimal 8859)
   defining Unicode char U+229D (decimal 8861)
   defining Unicode char U+229E (decimal 8862)
   defining Unicode char U+229F (decimal 8863)
   defining Unicode char U+22A0 (decimal 8864)
   defining Unicode char U+22A1 (decimal 8865)
   defining Unicode char U+22A2 (decimal 8866)
   defining Unicode char U+22A3 (decimal 8867)
   defining Unicode char U+22A4 (decimal 8868)
   defining Unicode char U+22A5 (decimal 8869)
   defining Unicode char U+22A7 (decimal 8871)
   defining Unicode char U+22A8 (decimal 8872)
   defining Unicode char U+22A9 (decimal 8873)
   defining Unicode char U+22AA (decimal 8874)
   defining Unicode char U+22AB (decimal 8875)
   defining Unicode char U+22AC (decimal 8876)
   defining Unicode char U+22AD (decimal 8877)
   defining Unicode char U+22AE (decimal 8878)
   defining Unicode char U+22AF (decimal 8879)
   defining Unicode char U+22B2 (decimal 8882)
   defining Unicode char U+22B3 (decimal 8883)
   defining Unicode char U+22B4 (decimal 8884)
   defining Unicode char U+22B5 (decimal 8885)
   defining Unicode char U+22B6 (decimal 8886)
   defining Unicode char U+22B7 (decimal 8887)
   defining Unicode char U+22B8 (decimal 8888)
   defining Unicode char U+22B9 (decimal 8889)
   defining Unicode char U+22BA (decimal 8890)
   defining Unicode char U+22BB (decimal 8891)
   defining Unicode char U+22BC (decimal 8892)
   defining Unicode char U+22BE (decimal 8894)
   defining Unicode char U+22C0 (decimal 8896)
   defining Unicode char U+22C1 (decimal 8897)
   defining Unicode char U+22C2 (decimal 8898)
   defining Unicode char U+22C3 (decimal 8899)
   defining Unicode char U+22C4 (decimal 8900)
   defining Unicode char U+22C5 (decimal 8901)
   defining Unicode char U+22C6 (decimal 8902)
   defining Unicode char U+22C7 (decimal 8903)
   defining Unicode char U+22C8 (decimal 8904)
   defining Unicode char U+22C9 (decimal 8905)
   defining Unicode char U+22CA (decimal 8906)
   defining Unicode char U+22CB (decimal 8907)
   defining Unicode char U+22CC (decimal 8908)
   defining Unicode char U+22CD (decimal 8909)
   defining Unicode char U+22CE (decimal 8910)
   defining Unicode char U+22CF (decimal 8911)
   defining Unicode char U+22D0 (decimal 8912)
   defining Unicode char U+22D1 (decimal 8913)
   defining Unicode char U+22D2 (decimal 8914)
   defining Unicode char U+22D3 (decimal 8915)
   defining Unicode char U+22D4 (decimal 8916)
   defining Unicode char U+22D6 (decimal 8918)
   defining Unicode char U+22D7 (decimal 8919)
   defining Unicode char U+22D8 (decimal 8920)
   defining Unicode char U+22D9 (decimal 8921)
   defining Unicode char U+22DA (decimal 8922)
   defining Unicode char U+22DB (decimal 8923)
   defining Unicode char U+22DC (decimal 8924)
   defining Unicode char U+22DD (decimal 8925)
   defining Unicode char U+22DE (decimal 8926)
   defining Unicode char U+22DF (decimal 8927)
   defining Unicode char U+22E2 (decimal 8930)
   defining Unicode char U+22E3 (decimal 8931)
   defining Unicode char U+22E5 (decimal 8933)
   defining Unicode char U+22E6 (decimal 8934)
   defining Unicode char U+22E7 (decimal 8935)
   defining Unicode char U+22E8 (decimal 8936)
   defining Unicode char U+22E9 (decimal 8937)
   defining Unicode char U+22EA (decimal 8938)
   defining Unicode char U+22EB (decimal 8939)
   defining Unicode char U+22EC (decimal 8940)
   defining Unicode char U+22ED (decimal 8941)
   defining Unicode char U+22EE (decimal 8942)
   defining Unicode char U+22EF (decimal 8943)
   defining Unicode char U+22F0 (decimal 8944)
   defining Unicode char U+22F1 (decimal 8945)
   defining Unicode char U+2306 (decimal 8966)
   defining Unicode char U+2308 (decimal 8968)
   defining Unicode char U+2309 (decimal 8969)
   defining Unicode char U+230A (decimal 8970)
   defining Unicode char U+230B (decimal 8971)
   defining Unicode char U+2315 (decimal 8981)
   defining Unicode char U+2316 (decimal 8982)
   defining Unicode char U+231C (decimal 8988)
   defining Unicode char U+231D (decimal 8989)
   defining Unicode char U+231E (decimal 8990)
   defining Unicode char U+231F (decimal 8991)
   defining Unicode char U+2322 (decimal 8994)
   defining Unicode char U+2323 (decimal 8995)
   defining Unicode char U+2329 (decimal 9001)
   defining Unicode char U+232A (decimal 9002)
   defining Unicode char U+2423 (decimal 9251)
   defining Unicode char U+2460 (decimal 9312)
   defining Unicode char U+2461 (decimal 9313)
   defining Unicode char U+2462 (decimal 9314)
   defining Unicode char U+2463 (decimal 9315)
   defining Unicode char U+2464 (decimal 9316)
   defining Unicode char U+2465 (decimal 9317)
   defining Unicode char U+2466 (decimal 9318)
   defining Unicode char U+2467 (decimal 9319)
   defining Unicode char U+2468 (decimal 9320)
   defining Unicode char U+2469 (decimal 9321)
   defining Unicode char U+24C8 (decimal 9416)
   defining Unicode char U+2519 (decimal 9497)
   defining Unicode char U+25A0 (decimal 9632)
   defining Unicode char U+25A1 (decimal 9633)
   defining Unicode char U+25AA (decimal 9642)
   defining Unicode char U+25AD (decimal 9645)
   defining Unicode char U+25AF (decimal 9647)
   defining Unicode char U+25B1 (decimal 9649)
   defining Unicode char U+25B2 (decimal 9650)
   defining Unicode char U+25B3 (decimal 9651)
   defining Unicode char U+25B4 (decimal 9652)
   defining Unicode char U+25B5 (decimal 9653)
   defining Unicode char U+25B8 (decimal 9656)
   defining Unicode char U+25B9 (decimal 9657)
   defining Unicode char U+25BC (decimal 9660)
   defining Unicode char U+25BD (decimal 9661)
   defining Unicode char U+25BE (decimal 9662)
   defining Unicode char U+25BF (decimal 9663)
   defining Unicode char U+25C2 (decimal 9666)
   defining Unicode char U+25C3 (decimal 9667)
   defining Unicode char U+25C6 (decimal 9670)
   defining Unicode char U+25CA (decimal 9674)
   defining Unicode char U+25CB (decimal 9675)
   defining Unicode char U+25CF (decimal 9679)
   defining Unicode char U+25D0 (decimal 9680)
   defining Unicode char U+25D1 (decimal 9681)
   defining Unicode char U+25D2 (decimal 9682)
   defining Unicode char U+25D7 (decimal 9687)
   defining Unicode char U+25D8 (decimal 9688)
   defining Unicode char U+25E7 (decimal 9703)
   defining Unicode char U+25E8 (decimal 9704)
   defining Unicode char U+25EA (decimal 9706)
   defining Unicode char U+25EF (decimal 9711)
   defining Unicode char U+2605 (decimal 9733)
   defining Unicode char U+2606 (decimal 9734)
   defining Unicode char U+260E (decimal 9742)
   defining Unicode char U+261B (decimal 9755)
   defining Unicode char U+261E (decimal 9758)
   defining Unicode char U+263E (decimal 9790)
   defining Unicode char U+263F (decimal 9791)
   defining Unicode char U+2640 (decimal 9792)
   defining Unicode char U+2642 (decimal 9794)
   defining Unicode char U+2643 (decimal 9795)
   defining Unicode char U+2644 (decimal 9796)
   defining Unicode char U+2645 (decimal 9797)
   defining Unicode char U+2646 (decimal 9798)
   defining Unicode char U+2647 (decimal 9799)
   defining Unicode char U+2648 (decimal 9800)
   defining Unicode char U+2649 (decimal 9801)
   defining Unicode char U+264A (decimal 9802)
   defining Unicode char U+264B (decimal 9803)
   defining Unicode char U+264C (decimal 9804)
   defining Unicode char U+264D (decimal 9805)
   defining Unicode char U+264E (decimal 9806)
   defining Unicode char U+264F (decimal 9807)
   defining Unicode char U+2650 (decimal 9808)
   defining Unicode char U+2651 (decimal 9809)
   defining Unicode char U+2652 (decimal 9810)
   defining Unicode char U+2653 (decimal 9811)
   defining Unicode char U+2660 (decimal 9824)
   defining Unicode char U+2662 (decimal 9826)
   defining Unicode char U+2663 (decimal 9827)
   defining Unicode char U+2665 (decimal 9829)
   defining Unicode char U+2666 (decimal 9830)
   defining Unicode char U+2669 (decimal 9833)
   defining Unicode char U+266A (decimal 9834)
   defining Unicode char U+266D (decimal 9837)
   defining Unicode char U+266E (decimal 9838)
   defining Unicode char U+266F (decimal 9839)
   defining Unicode char U+2701 (decimal 9985)
   defining Unicode char U+2702 (decimal 9986)
   defining Unicode char U+2703 (decimal 9987)
   defining Unicode char U+2704 (decimal 9988)
   defining Unicode char U+2706 (decimal 9990)
   defining Unicode char U+2707 (decimal 9991)
   defining Unicode char U+2708 (decimal 9992)
   defining Unicode char U+2709 (decimal 9993)
   defining Unicode char U+270C (decimal 9996)
   defining Unicode char U+270D (decimal 9997)
   defining Unicode char U+270E (decimal 9998)
   defining Unicode char U+270F (decimal 9999)
   defining Unicode char U+2710 (decimal 10000)
   defining Unicode char U+2711 (decimal 10001)
   defining Unicode char U+2712 (decimal 10002)
   defining Unicode char U+2713 (decimal 10003)
   defining Unicode char U+2714 (decimal 10004)
   defining Unicode char U+2715 (decimal 10005)
   defining Unicode char U+2716 (decimal 10006)
   defining Unicode char U+2717 (decimal 10007)
   defining Unicode char U+2718 (decimal 10008)
   defining Unicode char U+2719 (decimal 10009)
   defining Unicode char U+271A (decimal 10010)
   defining Unicode char U+271B (decimal 10011)
   defining Unicode char U+271C (decimal 10012)
   defining Unicode char U+271D (decimal 10013)
   defining Unicode char U+271E (decimal 10014)
   defining Unicode char U+271F (decimal 10015)
   defining Unicode char U+2720 (decimal 10016)
   defining Unicode char U+2721 (decimal 10017)
   defining Unicode char U+2722 (decimal 10018)
   defining Unicode char U+2723 (decimal 10019)
   defining Unicode char U+2724 (decimal 10020)
   defining Unicode char U+2725 (decimal 10021)
   defining Unicode char U+2726 (decimal 10022)
   defining Unicode char U+2727 (decimal 10023)
   defining Unicode char U+2729 (decimal 10025)
   defining Unicode char U+272A (decimal 10026)
   defining Unicode char U+272B (decimal 10027)
   defining Unicode char U+272C (decimal 10028)
   defining Unicode char U+272D (decimal 10029)
   defining Unicode char U+272E (decimal 10030)
   defining Unicode char U+272F (decimal 10031)
   defining Unicode char U+2730 (decimal 10032)
   defining Unicode char U+2731 (decimal 10033)
   defining Unicode char U+2732 (decimal 10034)
   defining Unicode char U+2733 (decimal 10035)
   defining Unicode char U+2734 (decimal 10036)
   defining Unicode char U+2735 (decimal 10037)
   defining Unicode char U+2736 (decimal 10038)
   defining Unicode char U+2737 (decimal 10039)
   defining Unicode char U+2738 (decimal 10040)
   defining Unicode char U+2739 (decimal 10041)
   defining Unicode char U+273A (decimal 10042)
   defining Unicode char U+273B (decimal 10043)
   defining Unicode char U+273C (decimal 10044)
   defining Unicode char U+273D (decimal 10045)
   defining Unicode char U+273E (decimal 10046)
   defining Unicode char U+273F (decimal 10047)
   defining Unicode char U+2740 (decimal 10048)
   defining Unicode char U+2741 (decimal 10049)
   defining Unicode char U+2742 (decimal 10050)
   defining Unicode char U+2743 (decimal 10051)
   defining Unicode char U+2744 (decimal 10052)
   defining Unicode char U+2745 (decimal 10053)
   defining Unicode char U+2746 (decimal 10054)
   defining Unicode char U+2747 (decimal 10055)
   defining Unicode char U+2748 (decimal 10056)
   defining Unicode char U+2749 (decimal 10057)
   defining Unicode char U+274A (decimal 10058)
   defining Unicode char U+274B (decimal 10059)
   defining Unicode char U+274D (decimal 10061)
   defining Unicode char U+274F (decimal 10063)
   defining Unicode char U+2750 (decimal 10064)
   defining Unicode char U+2751 (decimal 10065)
   defining Unicode char U+2752 (decimal 10066)
   defining Unicode char U+2756 (decimal 10070)
   defining Unicode char U+2758 (decimal 10072)
   defining Unicode char U+2759 (decimal 10073)
   defining Unicode char U+275A (decimal 10074)
   defining Unicode char U+275B (decimal 10075)
   defining Unicode char U+275C (decimal 10076)
   defining Unicode char U+275D (decimal 10077)
   defining Unicode char U+275E (decimal 10078)
   defining Unicode char U+2761 (decimal 10081)
   defining Unicode char U+2762 (decimal 10082)
   defining Unicode char U+2763 (decimal 10083)
   defining Unicode char U+2764 (decimal 10084)
   defining Unicode char U+2765 (decimal 10085)
   defining Unicode char U+2766 (decimal 10086)
   defining Unicode char U+2767 (decimal 10087)
   defining Unicode char U+2776 (decimal 10102)
   defining Unicode char U+2777 (decimal 10103)
   defining Unicode char U+2778 (decimal 10104)
   defining Unicode char U+2779 (decimal 10105)
   defining Unicode char U+277A (decimal 10106)
   defining Unicode char U+277B (decimal 10107)
   defining Unicode char U+277C (decimal 10108)
   defining Unicode char U+277D (decimal 10109)
   defining Unicode char U+277E (decimal 10110)
   defining Unicode char U+277F (decimal 10111)
   defining Unicode char U+2780 (decimal 10112)
   defining Unicode char U+2781 (decimal 10113)
   defining Unicode char U+2782 (decimal 10114)
   defining Unicode char U+2783 (decimal 10115)
   defining Unicode char U+2784 (decimal 10116)
   defining Unicode char U+2785 (decimal 10117)
   defining Unicode char U+2786 (decimal 10118)
   defining Unicode char U+2787 (decimal 10119)
   defining Unicode char U+2788 (decimal 10120)
   defining Unicode char U+2789 (decimal 10121)
   defining Unicode char U+278A (decimal 10122)
   defining Unicode char U+278B (decimal 10123)
   defining Unicode char U+278C (decimal 10124)
   defining Unicode char U+278D (decimal 10125)
   defining Unicode char U+278E (decimal 10126)
   defining Unicode char U+278F (decimal 10127)
   defining Unicode char U+2790 (decimal 10128)
   defining Unicode char U+2791 (decimal 10129)
   defining Unicode char U+2792 (decimal 10130)
   defining Unicode char U+2793 (decimal 10131)
   defining Unicode char U+2794 (decimal 10132)
   defining Unicode char U+2798 (decimal 10136)
   defining Unicode char U+2799 (decimal 10137)
   defining Unicode char U+279A (decimal 10138)
   defining Unicode char U+279B (decimal 10139)
   defining Unicode char U+279C (decimal 10140)
   defining Unicode char U+279D (decimal 10141)
   defining Unicode char U+279E (decimal 10142)
   defining Unicode char U+279F (decimal 10143)
   defining Unicode char U+27A0 (decimal 10144)
   defining Unicode char U+27A1 (decimal 10145)
   defining Unicode char U+27A2 (decimal 10146)
   defining Unicode char U+27A3 (decimal 10147)
   defining Unicode char U+27A4 (decimal 10148)
   defining Unicode char U+27A5 (decimal 10149)
   defining Unicode char U+27A6 (decimal 10150)
   defining Unicode char U+27A7 (decimal 10151)
   defining Unicode char U+27A8 (decimal 10152)
   defining Unicode char U+27A9 (decimal 10153)
   defining Unicode char U+27AA (decimal 10154)
   defining Unicode char U+27AB (decimal 10155)
   defining Unicode char U+27AC (decimal 10156)
   defining Unicode char U+27AD (decimal 10157)
   defining Unicode char U+27AE (decimal 10158)
   defining Unicode char U+27AF (decimal 10159)
   defining Unicode char U+27B1 (decimal 10161)
   defining Unicode char U+27B2 (decimal 10162)
   defining Unicode char U+27B3 (decimal 10163)
   defining Unicode char U+27B4 (decimal 10164)
   defining Unicode char U+27B5 (decimal 10165)
   defining Unicode char U+27B6 (decimal 10166)
   defining Unicode char U+27B7 (decimal 10167)
   defining Unicode char U+27B8 (decimal 10168)
   defining Unicode char U+27B9 (decimal 10169)
   defining Unicode char U+27BA (decimal 10170)
   defining Unicode char U+27BB (decimal 10171)
   defining Unicode char U+27BC (decimal 10172)
   defining Unicode char U+27BD (decimal 10173)
   defining Unicode char U+27BE (decimal 10174)
   defining Unicode char U+2905 (decimal 10501)
   defining Unicode char U+2923 (decimal 10531)
   defining Unicode char U+2924 (decimal 10532)
   defining Unicode char U+2925 (decimal 10533)
   defining Unicode char U+2926 (decimal 10534)
   defining Unicode char U+2927 (decimal 10535)
   defining Unicode char U+2928 (decimal 10536)
   defining Unicode char U+2929 (decimal 10537)
   defining Unicode char U+292A (decimal 10538)
   defining Unicode char U+296E (decimal 10606)
   defining Unicode char U+296F (decimal 10607)
   defining Unicode char U+2933 (decimal 10547)
   defining Unicode char U+2936 (decimal 10550)
   defining Unicode char U+2937 (decimal 10551)
   defining Unicode char U+297C (decimal 10620)
   defining Unicode char U+297D (decimal 10621)
   defining Unicode char U+2A3F (decimal 10815)
   defining Unicode char U+2A06 (decimal 10758)
   defining Unicode char U+2A04 (decimal 10756)
   defining Unicode char U+2A3C (decimal 10812)
   defining Unicode char U+2A25 (decimal 10789)
   defining Unicode char U+2A2A (decimal 10794)
   defining Unicode char U+2A2D (decimal 10797)
   defining Unicode char U+2A2E (decimal 10798)
   defining Unicode char U+2A34 (decimal 10804)
   defining Unicode char U+2A35 (decimal 10805)
   defining Unicode char U+29B5 (decimal 10677)
   defining Unicode char U+2A5F (decimal 10847)
   defining Unicode char U+2994 (decimal 10644)
   defining Unicode char U+2993 (decimal 10643)
   defining Unicode char U+2997 (decimal 10647)
   defining Unicode char U+2998 (decimal 10648)
   defining Unicode char U+2A8A (decimal 10890)
   defining Unicode char U+2A88 (decimal 10888)
   defining Unicode char U+2A89 (decimal 10889)
   defining Unicode char U+2A87 (decimal 10887)
   defining Unicode char U+2AB9 (decimal 10937)
   defining Unicode char U+2AB5 (decimal 10933)
   defining Unicode char U+2ABA (decimal 10938)
   defining Unicode char U+2AB6 (decimal 10934)
   defining Unicode char U+2ACB (decimal 10955)
   defining Unicode char U+2ACC (decimal 10956)
   defining Unicode char U+210F (decimal 8463)
   defining Unicode char U+2A86 (decimal 10886)
   defining Unicode char U+2A8C (decimal 10892)
   defining Unicode char U+2A7E (decimal 10878)
   defining Unicode char U+2AA2 (decimal 10914)
   defining Unicode char U+2A85 (decimal 10885)
   defining Unicode char U+2A8B (decimal 10891)
   defining Unicode char U+2A7D (decimal 10877)
   defining Unicode char U+2AA1 (decimal 10913)
   defining Unicode char U+2AB7 (decimal 10935)
   defining Unicode char U+2AAF (decimal 10927)
   defining Unicode char U+2AB8 (decimal 10936)
   defining Unicode char U+2AB0 (decimal 10928)
   defining Unicode char U+2AC5 (decimal 10949)
   defining Unicode char U+2AC6 (decimal 10950)
   defining Unicode char U+2A9D (decimal 10909)
   defining Unicode char U+2A9E (decimal 10910)
   defining Unicode char U+2AEB (decimal 10987)
   defining Unicode char U+3DD (decimal 989)
   defining Unicode char U+21D4 (decimal 8660)
   defining Unicode char U+2A55 (decimal 10837)
   defining Unicode char U+2A56 (decimal 10838)
   defining Unicode char U+2057 (decimal 8279)
   defining Unicode char U+2063 (decimal 8291)
   defining Unicode char U+29DC (decimal 10716)
   defining Unicode char U+299C (decimal 10652)
   defining Unicode char U+2A53 (decimal 10835)
   defining Unicode char U+2A54 (decimal 10836)
   defining Unicode char U+2A16 (decimal 10774)
   defining Unicode char U+2985 (decimal 10629)
   defining Unicode char U+2986 (decimal 10630)
   defining Unicode char U+2A10 (decimal 10768)
   defining Unicode char U+2A0D (decimal 10765)
   defining Unicode char U+2A0F (decimal 10767)
   defining Unicode char U+2942 (decimal 10562)
   defining Unicode char U+2944 (decimal 10564)
   defining Unicode char U+2947 (decimal 10567)
   defining Unicode char U+2940 (decimal 10560)
   defining Unicode char U+2941 (decimal 10561)
   defining Unicode char U+23A3 (decimal 9123)
   defining Unicode char U+2985 (decimal 10629)
   defining Unicode char U+2980 (decimal 10624)
   defining Unicode char U+23A6 (decimal 9126)
   defining Unicode char U+2986 (decimal 10630)
   defining Unicode char U+2AF6 (decimal 10998)
   defining Unicode char U+2999 (decimal 10649)
   defining Unicode char U+2506 (decimal 9478)
   defining Unicode char U+2222 (decimal 8738)
   defining Unicode char U+29A0 (decimal 10656)
   defining Unicode char U+2A08 (decimal 10760)
   defining Unicode char U+2A07 (decimal 10759)
   defining Unicode char U+2A63 (decimal 10851)
   defining Unicode char U+2A5E (decimal 10846)
   defining Unicode char U+29CA (decimal 10698)
   defining Unicode char U+2A95 (decimal 10901)
   defining Unicode char U+2A96 (decimal 10902)
   defining Unicode char U+2054 (decimal 8276)
   defining Unicode char U+2055 (decimal 8277)
   defining Unicode char U+2A6E (decimal 10862)
   defining Unicode char U+29CB (decimal 10699)
   defining Unicode char U+2A2F (decimal 10799)
   defining Unicode char U+29B6 (decimal 10678)
   defining Unicode char U+2A05 (decimal 10757)
   defining Unicode char U+3F4 (decimal 1012)
   defining Unicode char U+213C (decimal 8508)
   defining Unicode char U+29EB (decimal 10731)
   defining Unicode char U+233D (decimal 9021)
   defining Unicode char U+2216 (decimal 8726)
   defining Unicode char U+FEFF (decimal 65279)
   defining Unicode char U+2005 (decimal 8197)
   defining Unicode char U+29CF (decimal 10703)
   defining Unicode char U+29D0 (decimal 10704)
   defining Unicode char U+2A75 (decimal 10869)
   defining Unicode char U+2912 (decimal 10514)
   defining Unicode char U+2913 (decimal 10515)
   defining Unicode char U+294E (decimal 10574)
   defining Unicode char U+2952 (decimal 10578)
   defining Unicode char U+2953 (decimal 10579)
   defining Unicode char U+295A (decimal 10586)
   defining Unicode char U+295B (decimal 10587)
   defining Unicode char U+2950 (decimal 10576)
   defining Unicode char U+2956 (decimal 10582)
   defining Unicode char U+2957 (decimal 10583)
   defining Unicode char U+295E (decimal 10590)
   defining Unicode char U+295F (decimal 10591)
   defining Unicode char U+294F (decimal 10575)
   defining Unicode char U+2954 (decimal 10580)
   defining Unicode char U+2955 (decimal 10581)
   defining Unicode char U+295C (decimal 10588)
   defining Unicode char U+295D (decimal 10589)
   defining Unicode char U+2951 (decimal 10577)
   defining Unicode char U+2958 (decimal 10584)
   defining Unicode char U+2959 (decimal 10585)
   defining Unicode char U+2960 (decimal 10592)
   defining Unicode char U+2961 (decimal 10593)
   defining Unicode char U+29F4 (decimal 10740)
   defining Unicode char U+2970 (decimal 10608)
   defining Unicode char U+3DA (decimal 986)
   defining Unicode char U+3DE (decimal 990)
   defining Unicode char U+3E0 (decimal 992)
   defining Unicode char U+300A (decimal 12298)
   defining Unicode char U+300B (decimal 12299)
   defining Unicode char U+3018 (decimal 12312)
   defining Unicode char U+3019 (decimal 12313)
   defining Unicode char U+301A (decimal 12314)
   defining Unicode char U+301B (decimal 12315)
   defining Unicode char U+FB00 (decimal 64256)
   defining Unicode char U+FB01 (decimal 64257)
   defining Unicode char U+FB02 (decimal 64258)
   defining Unicode char U+FB03 (decimal 64259)
   defining Unicode char U+FB04 (decimal 64260)
   defining Unicode char U+F6C4 (decimal 63172)
   defining Unicode char U+F6C5 (decimal 63173)
   defining Unicode char U+F6C6 (decimal 63174)
   defining Unicode char U+F6C7 (decimal 63175)
   defining Unicode char U+F6C8 (decimal 63176)
   defining Unicode char U+F6D1 (decimal 63185)
   defining Unicode char U+F6D2 (decimal 63186)
   defining Unicode char U+F6D3 (decimal 63187)
   defining Unicode char U+F6D4 (decimal 63188)
   defining Unicode char U+F6D5 (decimal 63189)
   defining Unicode char U+F6D6 (decimal 63190)
   defining Unicode char U+1D400 (decimal 119808)
   defining Unicode char U+1D401 (decimal 119809)
   defining Unicode char U+1D402 (decimal 119810)
   defining Unicode char U+1D403 (decimal 119811)
   defining Unicode char U+1D404 (decimal 119812)
   defining Unicode char U+1D405 (decimal 119813)
   defining Unicode char U+1D406 (decimal 119814)
   defining Unicode char U+1D407 (decimal 119815)
   defining Unicode char U+1D408 (decimal 119816)
   defining Unicode char U+1D409 (decimal 119817)
   defining Unicode char U+1D40A (decimal 119818)
   defining Unicode char U+1D40B (decimal 119819)
   defining Unicode char U+1D40C (decimal 119820)
   defining Unicode char U+1D40D (decimal 119821)
   defining Unicode char U+1D40E (decimal 119822)
   defining Unicode char U+1D40F (decimal 119823)
   defining Unicode char U+1D410 (decimal 119824)
   defining Unicode char U+1D411 (decimal 119825)
   defining Unicode char U+1D412 (decimal 119826)
   defining Unicode char U+1D413 (decimal 119827)
   defining Unicode char U+1D414 (decimal 119828)
   defining Unicode char U+1D415 (decimal 119829)
   defining Unicode char U+1D416 (decimal 119830)
   defining Unicode char U+1D417 (decimal 119831)
   defining Unicode char U+1D418 (decimal 119832)
   defining Unicode char U+1D419 (decimal 119833)
   defining Unicode char U+1D41A (decimal 119834)
   defining Unicode char U+1D41B (decimal 119835)
   defining Unicode char U+1D41C (decimal 119836)
   defining Unicode char U+1D41D (decimal 119837)
   defining Unicode char U+1D41E (decimal 119838)
   defining Unicode char U+1D41F (decimal 119839)
   defining Unicode char U+1D420 (decimal 119840)
   defining Unicode char U+1D421 (decimal 119841)
   defining Unicode char U+1D422 (decimal 119842)
   defining Unicode char U+1D423 (decimal 119843)
   defining Unicode char U+1D424 (decimal 119844)
   defining Unicode char U+1D425 (decimal 119845)
   defining Unicode char U+1D426 (decimal 119846)
   defining Unicode char U+1D427 (decimal 119847)
   defining Unicode char U+1D428 (decimal 119848)
   defining Unicode char U+1D429 (decimal 119849)
   defining Unicode char U+1D42A (decimal 119850)
   defining Unicode char U+1D42B (decimal 119851)
   defining Unicode char U+1D42C (decimal 119852)
   defining Unicode char U+1D42D (decimal 119853)
   defining Unicode char U+1D42E (decimal 119854)
   defining Unicode char U+1D42F (decimal 119855)
   defining Unicode char U+1D430 (decimal 119856)
   defining Unicode char U+1D431 (decimal 119857)
   defining Unicode char U+1D432 (decimal 119858)
   defining Unicode char U+1D433 (decimal 119859)
   defining Unicode char U+1D434 (decimal 119860)
   defining Unicode char U+1D435 (decimal 119861)
   defining Unicode char U+1D436 (decimal 119862)
   defining Unicode char U+1D437 (decimal 119863)
   defining Unicode char U+1D438 (decimal 119864)
   defining Unicode char U+1D439 (decimal 119865)
   defining Unicode char U+1D43A (decimal 119866)
   defining Unicode char U+1D43B (decimal 119867)
   defining Unicode char U+1D43C (decimal 119868)
   defining Unicode char U+1D43D (decimal 119869)
   defining Unicode char U+1D43E (decimal 119870)
   defining Unicode char U+1D43F (decimal 119871)
   defining Unicode char U+1D440 (decimal 119872)
   defining Unicode char U+1D441 (decimal 119873)
   defining Unicode char U+1D442 (decimal 119874)
   defining Unicode char U+1D443 (decimal 119875)
   defining Unicode char U+1D444 (decimal 119876)
   defining Unicode char U+1D445 (decimal 119877)
   defining Unicode char U+1D446 (decimal 119878)
   defining Unicode char U+1D447 (decimal 119879)
   defining Unicode char U+1D448 (decimal 119880)
   defining Unicode char U+1D449 (decimal 119881)
   defining Unicode char U+1D44A (decimal 119882)
   defining Unicode char U+1D44B (decimal 119883)
   defining Unicode char U+1D44C (decimal 119884)
   defining Unicode char U+1D44D (decimal 119885)
   defining Unicode char U+1D44E (decimal 119886)
   defining Unicode char U+1D44F (decimal 119887)
   defining Unicode char U+1D450 (decimal 119888)
   defining Unicode char U+1D451 (decimal 119889)
   defining Unicode char U+1D452 (decimal 119890)
   defining Unicode char U+1D453 (decimal 119891)
   defining Unicode char U+1D454 (decimal 119892)
   defining Unicode char U+1D456 (decimal 119894)
   defining Unicode char U+1D457 (decimal 119895)
   defining Unicode char U+1D458 (decimal 119896)
   defining Unicode char U+1D459 (decimal 119897)
   defining Unicode char U+1D45A (decimal 119898)
   defining Unicode char U+1D45B (decimal 119899)
   defining Unicode char U+1D45C (decimal 119900)
   defining Unicode char U+1D45D (decimal 119901)
   defining Unicode char U+1D45E (decimal 119902)
   defining Unicode char U+1D45F (decimal 119903)
   defining Unicode char U+1D460 (decimal 119904)
   defining Unicode char U+1D461 (decimal 119905)
   defining Unicode char U+1D462 (decimal 119906)
   defining Unicode char U+1D463 (decimal 119907)
   defining Unicode char U+1D464 (decimal 119908)
   defining Unicode char U+1D465 (decimal 119909)
   defining Unicode char U+1D466 (decimal 119910)
   defining Unicode char U+1D467 (decimal 119911)
   defining Unicode char U+1D468 (decimal 119912)
   defining Unicode char U+1D469 (decimal 119913)
   defining Unicode char U+1D46A (decimal 119914)
   defining Unicode char U+1D46B (decimal 119915)
   defining Unicode char U+1D46C (decimal 119916)
   defining Unicode char U+1D46D (decimal 119917)
   defining Unicode char U+1D46E (decimal 119918)
   defining Unicode char U+1D46F (decimal 119919)
   defining Unicode char U+1D470 (decimal 119920)
   defining Unicode char U+1D471 (decimal 119921)
   defining Unicode char U+1D472 (decimal 119922)
   defining Unicode char U+1D473 (decimal 119923)
   defining Unicode char U+1D474 (decimal 119924)
   defining Unicode char U+1D475 (decimal 119925)
   defining Unicode char U+1D476 (decimal 119926)
   defining Unicode char U+1D477 (decimal 119927)
   defining Unicode char U+1D478 (decimal 119928)
   defining Unicode char U+1D479 (decimal 119929)
   defining Unicode char U+1D47A (decimal 119930)
   defining Unicode char U+1D47B (decimal 119931)
   defining Unicode char U+1D47C (decimal 119932)
   defining Unicode char U+1D47D (decimal 119933)
   defining Unicode char U+1D47E (decimal 119934)
   defining Unicode char U+1D47F (decimal 119935)
   defining Unicode char U+1D480 (decimal 119936)
   defining Unicode char U+1D481 (decimal 119937)
   defining Unicode char U+1D482 (decimal 119938)
   defining Unicode char U+1D483 (decimal 119939)
   defining Unicode char U+1D484 (decimal 119940)
   defining Unicode char U+1D485 (decimal 119941)
   defining Unicode char U+1D486 (decimal 119942)
   defining Unicode char U+1D487 (decimal 119943)
   defining Unicode char U+1D488 (decimal 119944)
   defining Unicode char U+1D489 (decimal 119945)
   defining Unicode char U+1D48A (decimal 119946)
   defining Unicode char U+1D48B (decimal 119947)
   defining Unicode char U+1D48C (decimal 119948)
   defining Unicode char U+1D48D (decimal 119949)
   defining Unicode char U+1D48E (decimal 119950)
   defining Unicode char U+1D48F (decimal 119951)
   defining Unicode char U+1D490 (decimal 119952)
   defining Unicode char U+1D491 (decimal 119953)
   defining Unicode char U+1D492 (decimal 119954)
   defining Unicode char U+1D493 (decimal 119955)
   defining Unicode char U+1D494 (decimal 119956)
   defining Unicode char U+1D495 (decimal 119957)
   defining Unicode char U+1D496 (decimal 119958)
   defining Unicode char U+1D497 (decimal 119959)
   defining Unicode char U+1D498 (decimal 119960)
   defining Unicode char U+1D499 (decimal 119961)
   defining Unicode char U+1D49A (decimal 119962)
   defining Unicode char U+1D49B (decimal 119963)
   defining Unicode char U+1D49C (decimal 119964)
   defining Unicode char U+1D49E (decimal 119966)
   defining Unicode char U+1D49F (decimal 119967)
   defining Unicode char U+1D4A2 (decimal 119970)
   defining Unicode char U+1D4A5 (decimal 119973)
   defining Unicode char U+1D4A6 (decimal 119974)
   defining Unicode char U+1D4A9 (decimal 119977)
   defining Unicode char U+1D4AA (decimal 119978)
   defining Unicode char U+1D4AB (decimal 119979)
   defining Unicode char U+1D4AC (decimal 119980)
   defining Unicode char U+1D4AE (decimal 119982)
   defining Unicode char U+1D4AF (decimal 119983)
   defining Unicode char U+1D4B0 (decimal 119984)
   defining Unicode char U+1D4B1 (decimal 119985)
   defining Unicode char U+1D4B2 (decimal 119986)
   defining Unicode char U+1D4B3 (decimal 119987)
   defining Unicode char U+1D4B4 (decimal 119988)
   defining Unicode char U+1D4B5 (decimal 119989)
   defining Unicode char U+1D4B6 (decimal 119990)
   defining Unicode char U+1D4B7 (decimal 119991)
   defining Unicode char U+1D4B8 (decimal 119992)
   defining Unicode char U+1D4B9 (decimal 119993)
   defining Unicode char U+1D4BB (decimal 119995)
   defining Unicode char U+1D4BD (decimal 119997)
   defining Unicode char U+1D4BE (decimal 119998)
   defining Unicode char U+1D4BF (decimal 119999)
   defining Unicode char U+1D4C0 (decimal 120000)
   defining Unicode char U+1D4C2 (decimal 120002)
   defining Unicode char U+1D4C3 (decimal 120003)
   defining Unicode char U+1D4C5 (decimal 120005)
   defining Unicode char U+1D4C6 (decimal 120006)
   defining Unicode char U+1D4C7 (decimal 120007)
   defining Unicode char U+1D4C8 (decimal 120008)
   defining Unicode char U+1D4C9 (decimal 120009)
   defining Unicode char U+1D4CA (decimal 120010)
   defining Unicode char U+1D4CB (decimal 120011)
   defining Unicode char U+1D4CC (decimal 120012)
   defining Unicode char U+1D4CD (decimal 120013)
   defining Unicode char U+1D4CE (decimal 120014)
   defining Unicode char U+1D4CF (decimal 120015)
   defining Unicode char U+1D4D0 (decimal 120016)
   defining Unicode char U+1D4D1 (decimal 120017)
   defining Unicode char U+1D4D2 (decimal 120018)
   defining Unicode char U+1D4D3 (decimal 120019)
   defining Unicode char U+1D4D4 (decimal 120020)
   defining Unicode char U+1D4D5 (decimal 120021)
   defining Unicode char U+1D4D6 (decimal 120022)
   defining Unicode char U+1D4D7 (decimal 120023)
   defining Unicode char U+1D4D8 (decimal 120024)
   defining Unicode char U+1D4D9 (decimal 120025)
   defining Unicode char U+1D4DA (decimal 120026)
   defining Unicode char U+1D4DB (decimal 120027)
   defining Unicode char U+1D4DC (decimal 120028)
   defining Unicode char U+1D4DD (decimal 120029)
   defining Unicode char U+1D4DE (decimal 120030)
   defining Unicode char U+1D4DF (decimal 120031)
   defining Unicode char U+1D4E0 (decimal 120032)
   defining Unicode char U+1D4E1 (decimal 120033)
   defining Unicode char U+1D4E2 (decimal 120034)
   defining Unicode char U+1D4E3 (decimal 120035)
   defining Unicode char U+1D4E4 (decimal 120036)
   defining Unicode char U+1D4E5 (decimal 120037)
   defining Unicode char U+1D4E6 (decimal 120038)
   defining Unicode char U+1D4E7 (decimal 120039)
   defining Unicode char U+1D4E8 (decimal 120040)
   defining Unicode char U+1D4E9 (decimal 120041)
   defining Unicode char U+1D4EA (decimal 120042)
   defining Unicode char U+1D4EB (decimal 120043)
   defining Unicode char U+1D4EC (decimal 120044)
   defining Unicode char U+1D4ED (decimal 120045)
   defining Unicode char U+1D4EE (decimal 120046)
   defining Unicode char U+1D4EF (decimal 120047)
   defining Unicode char U+1D4F0 (decimal 120048)
   defining Unicode char U+1D4F1 (decimal 120049)
   defining Unicode char U+1D4F2 (decimal 120050)
   defining Unicode char U+1D4F3 (decimal 120051)
   defining Unicode char U+1D4F4 (decimal 120052)
   defining Unicode char U+1D4F5 (decimal 120053)
   defining Unicode char U+1D4F6 (decimal 120054)
   defining Unicode char U+1D4F7 (decimal 120055)
   defining Unicode char U+1D4F8 (decimal 120056)
   defining Unicode char U+1D4F9 (decimal 120057)
   defining Unicode char U+1D4FA (decimal 120058)
   defining Unicode char U+1D4FB (decimal 120059)
   defining Unicode char U+1D4FC (decimal 120060)
   defining Unicode char U+1D4FD (decimal 120061)
   defining Unicode char U+1D4FE (decimal 120062)
   defining Unicode char U+1D4FF (decimal 120063)
   defining Unicode char U+1D500 (decimal 120064)
   defining Unicode char U+1D501 (decimal 120065)
   defining Unicode char U+1D502 (decimal 120066)
   defining Unicode char U+1D503 (decimal 120067)
   defining Unicode char U+1D504 (decimal 120068)
   defining Unicode char U+1D505 (decimal 120069)
   defining Unicode char U+1D507 (decimal 120071)
   defining Unicode char U+1D508 (decimal 120072)
   defining Unicode char U+1D509 (decimal 120073)
   defining Unicode char U+1D50A (decimal 120074)
   defining Unicode char U+1D50D (decimal 120077)
   defining Unicode char U+1D50E (decimal 120078)
   defining Unicode char U+1D50F (decimal 120079)
   defining Unicode char U+1D510 (decimal 120080)
   defining Unicode char U+1D511 (decimal 120081)
   defining Unicode char U+1D512 (decimal 120082)
   defining Unicode char U+1D513 (decimal 120083)
   defining Unicode char U+1D514 (decimal 120084)
   defining Unicode char U+1D516 (decimal 120086)
   defining Unicode char U+1D517 (decimal 120087)
   defining Unicode char U+1D518 (decimal 120088)
   defining Unicode char U+1D519 (decimal 120089)
   defining Unicode char U+1D51A (decimal 120090)
   defining Unicode char U+1D51B (decimal 120091)
   defining Unicode char U+1D51C (decimal 120092)
   defining Unicode char U+1D51E (decimal 120094)
   defining Unicode char U+1D51F (decimal 120095)
   defining Unicode char U+1D520 (decimal 120096)
   defining Unicode char U+1D521 (decimal 120097)
   defining Unicode char U+1D522 (decimal 120098)
   defining Unicode char U+1D523 (decimal 120099)
   defining Unicode char U+1D524 (decimal 120100)
   defining Unicode char U+1D525 (decimal 120101)
   defining Unicode char U+1D526 (decimal 120102)
   defining Unicode char U+1D527 (decimal 120103)
   defining Unicode char U+1D528 (decimal 120104)
   defining Unicode char U+1D529 (decimal 120105)
   defining Unicode char U+1D52A (decimal 120106)
   defining Unicode char U+1D52B (decimal 120107)
   defining Unicode char U+1D52C (decimal 120108)
   defining Unicode char U+1D52D (decimal 120109)
   defining Unicode char U+1D52E (decimal 120110)
   defining Unicode char U+1D52F (decimal 120111)
   defining Unicode char U+1D530 (decimal 120112)
   defining Unicode char U+1D531 (decimal 120113)
   defining Unicode char U+1D532 (decimal 120114)
   defining Unicode char U+1D533 (decimal 120115)
   defining Unicode char U+1D534 (decimal 120116)
   defining Unicode char U+1D535 (decimal 120117)
   defining Unicode char U+1D536 (decimal 120118)
   defining Unicode char U+1D537 (decimal 120119)
   defining Unicode char U+1D538 (decimal 120120)
   defining Unicode char U+1D539 (decimal 120121)
   defining Unicode char U+1D53B (decimal 120123)
   defining Unicode char U+1D53C (decimal 120124)
   defining Unicode char U+1D53D (decimal 120125)
   defining Unicode char U+1D53E (decimal 120126)
   defining Unicode char U+1D540 (decimal 120128)
   defining Unicode char U+1D541 (decimal 120129)
   defining Unicode char U+1D542 (decimal 120130)
   defining Unicode char U+1D543 (decimal 120131)
   defining Unicode char U+1D544 (decimal 120132)
   defining Unicode char U+1D546 (decimal 120134)
   defining Unicode char U+1D54A (decimal 120138)
   defining Unicode char U+1D54B (decimal 120139)
   defining Unicode char U+1D54C (decimal 120140)
   defining Unicode char U+1D54D (decimal 120141)
   defining Unicode char U+1D54E (decimal 120142)
   defining Unicode char U+1D54F (decimal 120143)
   defining Unicode char U+1D550 (decimal 120144)
   defining Unicode char U+1D552 (decimal 120146)
   defining Unicode char U+1D553 (decimal 120147)
   defining Unicode char U+1D554 (decimal 120148)
   defining Unicode char U+1D555 (decimal 120149)
   defining Unicode char U+1D556 (decimal 120150)
   defining Unicode char U+1D557 (decimal 120151)
   defining Unicode char U+1D558 (decimal 120152)
   defining Unicode char U+1D559 (decimal 120153)
   defining Unicode char U+1D55A (decimal 120154)
   defining Unicode char U+1D55B (decimal 120155)
   defining Unicode char U+1D55C (decimal 120156)
   defining Unicode char U+1D55D (decimal 120157)
   defining Unicode char U+1D55E (decimal 120158)
   defining Unicode char U+1D55F (decimal 120159)
   defining Unicode char U+1D560 (decimal 120160)
   defining Unicode char U+1D561 (decimal 120161)
   defining Unicode char U+1D562 (decimal 120162)
   defining Unicode char U+1D563 (decimal 120163)
   defining Unicode char U+1D564 (decimal 120164)
   defining Unicode char U+1D565 (decimal 120165)
   defining Unicode char U+1D566 (decimal 120166)
   defining Unicode char U+1D567 (decimal 120167)
   defining Unicode char U+1D568 (decimal 120168)
   defining Unicode char U+1D569 (decimal 120169)
   defining Unicode char U+1D56A (decimal 120170)
   defining Unicode char U+1D56B (decimal 120171)
   defining Unicode char U+1D56C (decimal 120172)
   defining Unicode char U+1D56D (decimal 120173)
   defining Unicode char U+1D56E (decimal 120174)
   defining Unicode char U+1D56F (decimal 120175)
   defining Unicode char U+1D570 (decimal 120176)
   defining Unicode char U+1D571 (decimal 120177)
   defining Unicode char U+1D572 (decimal 120178)
   defining Unicode char U+1D573 (decimal 120179)
   defining Unicode char U+1D574 (decimal 120180)
   defining Unicode char U+1D575 (decimal 120181)
   defining Unicode char U+1D576 (decimal 120182)
   defining Unicode char U+1D577 (decimal 120183)
   defining Unicode char U+1D578 (decimal 120184)
   defining Unicode char U+1D579 (decimal 120185)
   defining Unicode char U+1D57A (decimal 120186)
   defining Unicode char U+1D57B (decimal 120187)
   defining Unicode char U+1D57C (decimal 120188)
   defining Unicode char U+1D57D (decimal 120189)
   defining Unicode char U+1D57E (decimal 120190)
   defining Unicode char U+1D57F (decimal 120191)
   defining Unicode char U+1D580 (decimal 120192)
   defining Unicode char U+1D581 (decimal 120193)
   defining Unicode char U+1D582 (decimal 120194)
   defining Unicode char U+1D583 (decimal 120195)
   defining Unicode char U+1D584 (decimal 120196)
   defining Unicode char U+1D585 (decimal 120197)
   defining Unicode char U+1D586 (decimal 120198)
   defining Unicode char U+1D587 (decimal 120199)
   defining Unicode char U+1D588 (decimal 120200)
   defining Unicode char U+1D589 (decimal 120201)
   defining Unicode char U+1D58A (decimal 120202)
   defining Unicode char U+1D58B (decimal 120203)
   defining Unicode char U+1D58C (decimal 120204)
   defining Unicode char U+1D58D (decimal 120205)
   defining Unicode char U+1D58E (decimal 120206)
   defining Unicode char U+1D58F (decimal 120207)
   defining Unicode char U+1D590 (decimal 120208)
   defining Unicode char U+1D591 (decimal 120209)
   defining Unicode char U+1D592 (decimal 120210)
   defining Unicode char U+1D593 (decimal 120211)
   defining Unicode char U+1D594 (decimal 120212)
   defining Unicode char U+1D595 (decimal 120213)
   defining Unicode char U+1D596 (decimal 120214)
   defining Unicode char U+1D597 (decimal 120215)
   defining Unicode char U+1D598 (decimal 120216)
   defining Unicode char U+1D599 (decimal 120217)
   defining Unicode char U+1D59A (decimal 120218)
   defining Unicode char U+1D59B (decimal 120219)
   defining Unicode char U+1D59C (decimal 120220)
   defining Unicode char U+1D59D (decimal 120221)
   defining Unicode char U+1D59E (decimal 120222)
   defining Unicode char U+1D59F (decimal 120223)
   defining Unicode char U+1D5A0 (decimal 120224)
   defining Unicode char U+1D5A1 (decimal 120225)
   defining Unicode char U+1D5A2 (decimal 120226)
   defining Unicode char U+1D5A3 (decimal 120227)
   defining Unicode char U+1D5A4 (decimal 120228)
   defining Unicode char U+1D5A5 (decimal 120229)
   defining Unicode char U+1D5A6 (decimal 120230)
   defining Unicode char U+1D5A7 (decimal 120231)
   defining Unicode char U+1D5A8 (decimal 120232)
   defining Unicode char U+1D5A9 (decimal 120233)
   defining Unicode char U+1D5AA (decimal 120234)
   defining Unicode char U+1D5AB (decimal 120235)
   defining Unicode char U+1D5AC (decimal 120236)
   defining Unicode char U+1D5AD (decimal 120237)
   defining Unicode char U+1D5AE (decimal 120238)
   defining Unicode char U+1D5AF (decimal 120239)
   defining Unicode char U+1D5B0 (decimal 120240)
   defining Unicode char U+1D5B1 (decimal 120241)
   defining Unicode char U+1D5B2 (decimal 120242)
   defining Unicode char U+1D5B3 (decimal 120243)
   defining Unicode char U+1D5B4 (decimal 120244)
   defining Unicode char U+1D5B5 (decimal 120245)
   defining Unicode char U+1D5B6 (decimal 120246)
   defining Unicode char U+1D5B7 (decimal 120247)
   defining Unicode char U+1D5B8 (decimal 120248)
   defining Unicode char U+1D5B9 (decimal 120249)
   defining Unicode char U+1D5BA (decimal 120250)
   defining Unicode char U+1D5BB (decimal 120251)
   defining Unicode char U+1D5BC (decimal 120252)
   defining Unicode char U+1D5BD (decimal 120253)
   defining Unicode char U+1D5BE (decimal 120254)
   defining Unicode char U+1D5BF (decimal 120255)
   defining Unicode char U+1D5C0 (decimal 120256)
   defining Unicode char U+1D5C1 (decimal 120257)
   defining Unicode char U+1D5C2 (decimal 120258)
   defining Unicode char U+1D5C3 (decimal 120259)
   defining Unicode char U+1D5C4 (decimal 120260)
   defining Unicode char U+1D5C5 (decimal 120261)
   defining Unicode char U+1D5C6 (decimal 120262)
   defining Unicode char U+1D5C7 (decimal 120263)
   defining Unicode char U+1D5C8 (decimal 120264)
   defining Unicode char U+1D5C9 (decimal 120265)
   defining Unicode char U+1D5CA (decimal 120266)
   defining Unicode char U+1D5CB (decimal 120267)
   defining Unicode char U+1D5CC (decimal 120268)
   defining Unicode char U+1D5CD (decimal 120269)
   defining Unicode char U+1D5CE (decimal 120270)
   defining Unicode char U+1D5CF (decimal 120271)
   defining Unicode char U+1D5D0 (decimal 120272)
   defining Unicode char U+1D5D1 (decimal 120273)
   defining Unicode char U+1D5D2 (decimal 120274)
   defining Unicode char U+1D5D3 (decimal 120275)
   defining Unicode char U+1D5D4 (decimal 120276)
   defining Unicode char U+1D5D5 (decimal 120277)
   defining Unicode char U+1D5D6 (decimal 120278)
   defining Unicode char U+1D5D7 (decimal 120279)
   defining Unicode char U+1D5D8 (decimal 120280)
   defining Unicode char U+1D5D9 (decimal 120281)
   defining Unicode char U+1D5DA (decimal 120282)
   defining Unicode char U+1D5DB (decimal 120283)
   defining Unicode char U+1D5DC (decimal 120284)
   defining Unicode char U+1D5DD (decimal 120285)
   defining Unicode char U+1D5DE (decimal 120286)
   defining Unicode char U+1D5DF (decimal 120287)
   defining Unicode char U+1D5E0 (decimal 120288)
   defining Unicode char U+1D5E1 (decimal 120289)
   defining Unicode char U+1D5E2 (decimal 120290)
   defining Unicode char U+1D5E3 (decimal 120291)
   defining Unicode char U+1D5E4 (decimal 120292)
   defining Unicode char U+1D5E5 (decimal 120293)
   defining Unicode char U+1D5E6 (decimal 120294)
   defining Unicode char U+1D5E7 (decimal 120295)
   defining Unicode char U+1D5E8 (decimal 120296)
   defining Unicode char U+1D5E9 (decimal 120297)
   defining Unicode char U+1D5EA (decimal 120298)
   defining Unicode char U+1D5EB (decimal 120299)
   defining Unicode char U+1D5EC (decimal 120300)
   defining Unicode char U+1D5ED (decimal 120301)
   defining Unicode char U+1D5EE (decimal 120302)
   defining Unicode char U+1D5EF (decimal 120303)
   defining Unicode char U+1D5F0 (decimal 120304)
   defining Unicode char U+1D5F1 (decimal 120305)
   defining Unicode char U+1D5F2 (decimal 120306)
   defining Unicode char U+1D5F3 (decimal 120307)
   defining Unicode char U+1D5F4 (decimal 120308)
   defining Unicode char U+1D5F5 (decimal 120309)
   defining Unicode char U+1D5F6 (decimal 120310)
   defining Unicode char U+1D5F7 (decimal 120311)
   defining Unicode char U+1D5F8 (decimal 120312)
   defining Unicode char U+1D5F9 (decimal 120313)
   defining Unicode char U+1D5FA (decimal 120314)
   defining Unicode char U+1D5FB (decimal 120315)
   defining Unicode char U+1D5FC (decimal 120316)
   defining Unicode char U+1D5FD (decimal 120317)
   defining Unicode char U+1D5FE (decimal 120318)
   defining Unicode char U+1D5FF (decimal 120319)
   defining Unicode char U+1D600 (decimal 120320)
   defining Unicode char U+1D601 (decimal 120321)
   defining Unicode char U+1D602 (decimal 120322)
   defining Unicode char U+1D603 (decimal 120323)
   defining Unicode char U+1D604 (decimal 120324)
   defining Unicode char U+1D605 (decimal 120325)
   defining Unicode char U+1D606 (decimal 120326)
   defining Unicode char U+1D607 (decimal 120327)
   defining Unicode char U+1D608 (decimal 120328)
   defining Unicode char U+1D609 (decimal 120329)
   defining Unicode char U+1D60A (decimal 120330)
   defining Unicode char U+1D60B (decimal 120331)
   defining Unicode char U+1D60C (decimal 120332)
   defining Unicode char U+1D60D (decimal 120333)
   defining Unicode char U+1D60E (decimal 120334)
   defining Unicode char U+1D60F (decimal 120335)
   defining Unicode char U+1D610 (decimal 120336)
   defining Unicode char U+1D611 (decimal 120337)
   defining Unicode char U+1D612 (decimal 120338)
   defining Unicode char U+1D613 (decimal 120339)
   defining Unicode char U+1D614 (decimal 120340)
   defining Unicode char U+1D615 (decimal 120341)
   defining Unicode char U+1D616 (decimal 120342)
   defining Unicode char U+1D617 (decimal 120343)
   defining Unicode char U+1D618 (decimal 120344)
   defining Unicode char U+1D619 (decimal 120345)
   defining Unicode char U+1D61A (decimal 120346)
   defining Unicode char U+1D61B (decimal 120347)
   defining Unicode char U+1D61C (decimal 120348)
   defining Unicode char U+1D61D (decimal 120349)
   defining Unicode char U+1D61E (decimal 120350)
   defining Unicode char U+1D61F (decimal 120351)
   defining Unicode char U+1D620 (decimal 120352)
   defining Unicode char U+1D621 (decimal 120353)
   defining Unicode char U+1D622 (decimal 120354)
   defining Unicode char U+1D623 (decimal 120355)
   defining Unicode char U+1D624 (decimal 120356)
   defining Unicode char U+1D625 (decimal 120357)
   defining Unicode char U+1D626 (decimal 120358)
   defining Unicode char U+1D627 (decimal 120359)
   defining Unicode char U+1D628 (decimal 120360)
   defining Unicode char U+1D629 (decimal 120361)
   defining Unicode char U+1D62A (decimal 120362)
   defining Unicode char U+1D62B (decimal 120363)
   defining Unicode char U+1D62C (decimal 120364)
   defining Unicode char U+1D62D (decimal 120365)
   defining Unicode char U+1D62E (decimal 120366)
   defining Unicode char U+1D62F (decimal 120367)
   defining Unicode char U+1D630 (decimal 120368)
   defining Unicode char U+1D631 (decimal 120369)
   defining Unicode char U+1D632 (decimal 120370)
   defining Unicode char U+1D633 (decimal 120371)
   defining Unicode char U+1D634 (decimal 120372)
   defining Unicode char U+1D635 (decimal 120373)
   defining Unicode char U+1D636 (decimal 120374)
   defining Unicode char U+1D637 (decimal 120375)
   defining Unicode char U+1D638 (decimal 120376)
   defining Unicode char U+1D639 (decimal 120377)
   defining Unicode char U+1D63A (decimal 120378)
   defining Unicode char U+1D63B (decimal 120379)
   defining Unicode char U+1D63C (decimal 120380)
   defining Unicode char U+1D63D (decimal 120381)
   defining Unicode char U+1D63E (decimal 120382)
   defining Unicode char U+1D63F (decimal 120383)
   defining Unicode char U+1D640 (decimal 120384)
   defining Unicode char U+1D641 (decimal 120385)
   defining Unicode char U+1D642 (decimal 120386)
   defining Unicode char U+1D643 (decimal 120387)
   defining Unicode char U+1D644 (decimal 120388)
   defining Unicode char U+1D645 (decimal 120389)
   defining Unicode char U+1D646 (decimal 120390)
   defining Unicode char U+1D647 (decimal 120391)
   defining Unicode char U+1D648 (decimal 120392)
   defining Unicode char U+1D649 (decimal 120393)
   defining Unicode char U+1D64A (decimal 120394)
   defining Unicode char U+1D64B (decimal 120395)
   defining Unicode char U+1D64C (decimal 120396)
   defining Unicode char U+1D64D (decimal 120397)
   defining Unicode char U+1D64E (decimal 120398)
   defining Unicode char U+1D64F (decimal 120399)
   defining Unicode char U+1D650 (decimal 120400)
   defining Unicode char U+1D651 (decimal 120401)
   defining Unicode char U+1D652 (decimal 120402)
   defining Unicode char U+1D653 (decimal 120403)
   defining Unicode char U+1D654 (decimal 120404)
   defining Unicode char U+1D655 (decimal 120405)
   defining Unicode char U+1D656 (decimal 120406)
   defining Unicode char U+1D657 (decimal 120407)
   defining Unicode char U+1D658 (decimal 120408)
   defining Unicode char U+1D659 (decimal 120409)
   defining Unicode char U+1D65A (decimal 120410)
   defining Unicode char U+1D65B (decimal 120411)
   defining Unicode char U+1D65C (decimal 120412)
   defining Unicode char U+1D65D (decimal 120413)
   defining Unicode char U+1D65E (decimal 120414)
   defining Unicode char U+1D65F (decimal 120415)
   defining Unicode char U+1D660 (decimal 120416)
   defining Unicode char U+1D661 (decimal 120417)
   defining Unicode char U+1D662 (decimal 120418)
   defining Unicode char U+1D663 (decimal 120419)
   defining Unicode char U+1D664 (decimal 120420)
   defining Unicode char U+1D665 (decimal 120421)
   defining Unicode char U+1D666 (decimal 120422)
   defining Unicode char U+1D667 (decimal 120423)
   defining Unicode char U+1D668 (decimal 120424)
   defining Unicode char U+1D669 (decimal 120425)
   defining Unicode char U+1D66A (decimal 120426)
   defining Unicode char U+1D66B (decimal 120427)
   defining Unicode char U+1D66C (decimal 120428)
   defining Unicode char U+1D66D (decimal 120429)
   defining Unicode char U+1D66E (decimal 120430)
   defining Unicode char U+1D66F (decimal 120431)
   defining Unicode char U+1D670 (decimal 120432)
   defining Unicode char U+1D671 (decimal 120433)
   defining Unicode char U+1D672 (decimal 120434)
   defining Unicode char U+1D673 (decimal 120435)
   defining Unicode char U+1D674 (decimal 120436)
   defining Unicode char U+1D675 (decimal 120437)
   defining Unicode char U+1D676 (decimal 120438)
   defining Unicode char U+1D677 (decimal 120439)
   defining Unicode char U+1D678 (decimal 120440)
   defining Unicode char U+1D679 (decimal 120441)
   defining Unicode char U+1D67A (decimal 120442)
   defining Unicode char U+1D67B (decimal 120443)
   defining Unicode char U+1D67C (decimal 120444)
   defining Unicode char U+1D67D (decimal 120445)
   defining Unicode char U+1D67E (decimal 120446)
   defining Unicode char U+1D67F (decimal 120447)
   defining Unicode char U+1D680 (decimal 120448)
   defining Unicode char U+1D681 (decimal 120449)
   defining Unicode char U+1D682 (decimal 120450)
   defining Unicode char U+1D683 (decimal 120451)
   defining Unicode char U+1D684 (decimal 120452)
   defining Unicode char U+1D685 (decimal 120453)
   defining Unicode char U+1D686 (decimal 120454)
   defining Unicode char U+1D687 (decimal 120455)
   defining Unicode char U+1D688 (decimal 120456)
   defining Unicode char U+1D689 (decimal 120457)
   defining Unicode char U+1D68A (decimal 120458)
   defining Unicode char U+1D68B (decimal 120459)
   defining Unicode char U+1D68C (decimal 120460)
   defining Unicode char U+1D68D (decimal 120461)
   defining Unicode char U+1D68E (decimal 120462)
   defining Unicode char U+1D68F (decimal 120463)
   defining Unicode char U+1D690 (decimal 120464)
   defining Unicode char U+1D691 (decimal 120465)
   defining Unicode char U+1D692 (decimal 120466)
   defining Unicode char U+1D693 (decimal 120467)
   defining Unicode char U+1D694 (decimal 120468)
   defining Unicode char U+1D695 (decimal 120469)
   defining Unicode char U+1D696 (decimal 120470)
   defining Unicode char U+1D697 (decimal 120471)
   defining Unicode char U+1D698 (decimal 120472)
   defining Unicode char U+1D699 (decimal 120473)
   defining Unicode char U+1D69A (decimal 120474)
   defining Unicode char U+1D69B (decimal 120475)
   defining Unicode char U+1D69C (decimal 120476)
   defining Unicode char U+1D69D (decimal 120477)
   defining Unicode char U+1D69E (decimal 120478)
   defining Unicode char U+1D69F (decimal 120479)
   defining Unicode char U+1D6A0 (decimal 120480)
   defining Unicode char U+1D6A1 (decimal 120481)
   defining Unicode char U+1D6A2 (decimal 120482)
   defining Unicode char U+1D6A3 (decimal 120483)
   defining Unicode char U+1D6A8 (decimal 120488)
   defining Unicode char U+1D6A9 (decimal 120489)
   defining Unicode char U+1D6AA (decimal 120490)
   defining Unicode char U+1D6AB (decimal 120491)
   defining Unicode char U+1D6AC (decimal 120492)
   defining Unicode char U+1D6AD (decimal 120493)
   defining Unicode char U+1D6AE (decimal 120494)
   defining Unicode char U+1D6AF (decimal 120495)
   defining Unicode char U+1D6B0 (decimal 120496)
   defining Unicode char U+1D6B1 (decimal 120497)
   defining Unicode char U+1D6B2 (decimal 120498)
   defining Unicode char U+1D6B3 (decimal 120499)
   defining Unicode char U+1D6B4 (decimal 120500)
   defining Unicode char U+1D6B5 (decimal 120501)
   defining Unicode char U+1D6B6 (decimal 120502)
   defining Unicode char U+1D6B7 (decimal 120503)
   defining Unicode char U+1D6B8 (decimal 120504)
   defining Unicode char U+1D6B9 (decimal 120505)
   defining Unicode char U+1D6BA (decimal 120506)
   defining Unicode char U+1D6BB (decimal 120507)
   defining Unicode char U+1D6BC (decimal 120508)
   defining Unicode char U+1D6BD (decimal 120509)
   defining Unicode char U+1D6BE (decimal 120510)
   defining Unicode char U+1D6BF (decimal 120511)
   defining Unicode char U+1D6C0 (decimal 120512)
   defining Unicode char U+1D6C1 (decimal 120513)
   defining Unicode char U+1D6C2 (decimal 120514)
   defining Unicode char U+1D6C3 (decimal 120515)
   defining Unicode char U+1D6C4 (decimal 120516)
   defining Unicode char U+1D6C5 (decimal 120517)
   defining Unicode char U+1D6C6 (decimal 120518)
   defining Unicode char U+1D6C7 (decimal 120519)
   defining Unicode char U+1D6C8 (decimal 120520)
   defining Unicode char U+1D6C9 (decimal 120521)
   defining Unicode char U+1D6CA (decimal 120522)
   defining Unicode char U+1D6CB (decimal 120523)
   defining Unicode char U+1D6CC (decimal 120524)
   defining Unicode char U+1D6CD (decimal 120525)
   defining Unicode char U+1D6CE (decimal 120526)
   defining Unicode char U+1D6CF (decimal 120527)
   defining Unicode char U+1D6D0 (decimal 120528)
   defining Unicode char U+1D6D1 (decimal 120529)
   defining Unicode char U+1D6D2 (decimal 120530)
   defining Unicode char U+1D6D3 (decimal 120531)
   defining Unicode char U+1D6D4 (decimal 120532)
   defining Unicode char U+1D6D5 (decimal 120533)
   defining Unicode char U+1D6D6 (decimal 120534)
   defining Unicode char U+1D6D7 (decimal 120535)
   defining Unicode char U+1D6D8 (decimal 120536)
   defining Unicode char U+1D6D9 (decimal 120537)
   defining Unicode char U+1D6DA (decimal 120538)
   defining Unicode char U+1D6DB (decimal 120539)
   defining Unicode char U+1D6DC (decimal 120540)
   defining Unicode char U+1D6DD (decimal 120541)
   defining Unicode char U+1D6DE (decimal 120542)
   defining Unicode char U+1D6DF (decimal 120543)
   defining Unicode char U+1D6E0 (decimal 120544)
   defining Unicode char U+1D6E1 (decimal 120545)
   defining Unicode char U+1D6E2 (decimal 120546)
   defining Unicode char U+1D6E3 (decimal 120547)
   defining Unicode char U+1D6E4 (decimal 120548)
   defining Unicode char U+1D6E5 (decimal 120549)
   defining Unicode char U+1D6E6 (decimal 120550)
   defining Unicode char U+1D6E7 (decimal 120551)
   defining Unicode char U+1D6E8 (decimal 120552)
   defining Unicode char U+1D6E9 (decimal 120553)
   defining Unicode char U+1D6EA (decimal 120554)
   defining Unicode char U+1D6EB (decimal 120555)
   defining Unicode char U+1D6EC (decimal 120556)
   defining Unicode char U+1D6ED (decimal 120557)
   defining Unicode char U+1D6EE (decimal 120558)
   defining Unicode char U+1D6EF (decimal 120559)
   defining Unicode char U+1D6F0 (decimal 120560)
   defining Unicode char U+1D6F1 (decimal 120561)
   defining Unicode char U+1D6F2 (decimal 120562)
   defining Unicode char U+1D6F3 (decimal 120563)
   defining Unicode char U+1D6F4 (decimal 120564)
   defining Unicode char U+1D6F5 (decimal 120565)
   defining Unicode char U+1D6F6 (decimal 120566)
   defining Unicode char U+1D6F7 (decimal 120567)
   defining Unicode char U+1D6F8 (decimal 120568)
   defining Unicode char U+1D6F9 (decimal 120569)
   defining Unicode char U+1D6FA (decimal 120570)
   defining Unicode char U+1D6FB (decimal 120571)
   defining Unicode char U+1D6FC (decimal 120572)
   defining Unicode char U+1D6FD (decimal 120573)
   defining Unicode char U+1D6FE (decimal 120574)
   defining Unicode char U+1D6FF (decimal 120575)
   defining Unicode char U+1D700 (decimal 120576)
   defining Unicode char U+1D701 (decimal 120577)
   defining Unicode char U+1D702 (decimal 120578)
   defining Unicode char U+1D703 (decimal 120579)
   defining Unicode char U+1D704 (decimal 120580)
   defining Unicode char U+1D705 (decimal 120581)
   defining Unicode char U+1D706 (decimal 120582)
   defining Unicode char U+1D707 (decimal 120583)
   defining Unicode char U+1D708 (decimal 120584)
   defining Unicode char U+1D709 (decimal 120585)
   defining Unicode char U+1D70A (decimal 120586)
   defining Unicode char U+1D70B (decimal 120587)
   defining Unicode char U+1D70C (decimal 120588)
   defining Unicode char U+1D70D (decimal 120589)
   defining Unicode char U+1D70E (decimal 120590)
   defining Unicode char U+1D70F (decimal 120591)
   defining Unicode char U+1D710 (decimal 120592)
   defining Unicode char U+1D711 (decimal 120593)
   defining Unicode char U+1D712 (decimal 120594)
   defining Unicode char U+1D713 (decimal 120595)
   defining Unicode char U+1D714 (decimal 120596)
   defining Unicode char U+1D715 (decimal 120597)
   defining Unicode char U+1D716 (decimal 120598)
   defining Unicode char U+1D717 (decimal 120599)
   defining Unicode char U+1D718 (decimal 120600)
   defining Unicode char U+1D719 (decimal 120601)
   defining Unicode char U+1D71A (decimal 120602)
   defining Unicode char U+1D71B (decimal 120603)
   defining Unicode char U+1D71C (decimal 120604)
   defining Unicode char U+1D71D (decimal 120605)
   defining Unicode char U+1D71E (decimal 120606)
   defining Unicode char U+1D71F (decimal 120607)
   defining Unicode char U+1D720 (decimal 120608)
   defining Unicode char U+1D721 (decimal 120609)
   defining Unicode char U+1D722 (decimal 120610)
   defining Unicode char U+1D723 (decimal 120611)
   defining Unicode char U+1D724 (decimal 120612)
   defining Unicode char U+1D725 (decimal 120613)
   defining Unicode char U+1D726 (decimal 120614)
   defining Unicode char U+1D727 (decimal 120615)
   defining Unicode char U+1D728 (decimal 120616)
   defining Unicode char U+1D729 (decimal 120617)
   defining Unicode char U+1D72A (decimal 120618)
   defining Unicode char U+1D72B (decimal 120619)
   defining Unicode char U+1D72C (decimal 120620)
   defining Unicode char U+1D72D (decimal 120621)
   defining Unicode char U+1D72E (decimal 120622)
   defining Unicode char U+1D72F (decimal 120623)
   defining Unicode char U+1D730 (decimal 120624)
   defining Unicode char U+1D731 (decimal 120625)
   defining Unicode char U+1D732 (decimal 120626)
   defining Unicode char U+1D733 (decimal 120627)
   defining Unicode char U+1D734 (decimal 120628)
   defining Unicode char U+1D735 (decimal 120629)
   defining Unicode char U+1D736 (decimal 120630)
   defining Unicode char U+1D737 (decimal 120631)
   defining Unicode char U+1D738 (decimal 120632)
   defining Unicode char U+1D739 (decimal 120633)
   defining Unicode char U+1D73A (decimal 120634)
   defining Unicode char U+1D73B (decimal 120635)
   defining Unicode char U+1D73C (decimal 120636)
   defining Unicode char U+1D73D (decimal 120637)
   defining Unicode char U+1D73E (decimal 120638)
   defining Unicode char U+1D73F (decimal 120639)
   defining Unicode char U+1D740 (decimal 120640)
   defining Unicode char U+1D741 (decimal 120641)
   defining Unicode char U+1D742 (decimal 120642)
   defining Unicode char U+1D743 (decimal 120643)
   defining Unicode char U+1D744 (decimal 120644)
   defining Unicode char U+1D745 (decimal 120645)
   defining Unicode char U+1D746 (decimal 120646)
   defining Unicode char U+1D747 (decimal 120647)
   defining Unicode char U+1D748 (decimal 120648)
   defining Unicode char U+1D749 (decimal 120649)
   defining Unicode char U+1D74A (decimal 120650)
   defining Unicode char U+1D74B (decimal 120651)
   defining Unicode char U+1D74C (decimal 120652)
   defining Unicode char U+1D74D (decimal 120653)
   defining Unicode char U+1D74E (decimal 120654)
   defining Unicode char U+1D74F (decimal 120655)
   defining Unicode char U+1D750 (decimal 120656)
   defining Unicode char U+1D751 (decimal 120657)
   defining Unicode char U+1D752 (decimal 120658)
   defining Unicode char U+1D753 (decimal 120659)
   defining Unicode char U+1D754 (decimal 120660)
   defining Unicode char U+1D755 (decimal 120661)
   defining Unicode char U+1D756 (decimal 120662)
   defining Unicode char U+1D757 (decimal 120663)
   defining Unicode char U+1D758 (decimal 120664)
   defining Unicode char U+1D759 (decimal 120665)
   defining Unicode char U+1D75A (decimal 120666)
   defining Unicode char U+1D75B (decimal 120667)
   defining Unicode char U+1D75C (decimal 120668)
   defining Unicode char U+1D75D (decimal 120669)
   defining Unicode char U+1D75E (decimal 120670)
   defining Unicode char U+1D75F (decimal 120671)
   defining Unicode char U+1D760 (decimal 120672)
   defining Unicode char U+1D761 (decimal 120673)
   defining Unicode char U+1D762 (decimal 120674)
   defining Unicode char U+1D763 (decimal 120675)
   defining Unicode char U+1D764 (decimal 120676)
   defining Unicode char U+1D765 (decimal 120677)
   defining Unicode char U+1D766 (decimal 120678)
   defining Unicode char U+1D767 (decimal 120679)
   defining Unicode char U+1D768 (decimal 120680)
   defining Unicode char U+1D769 (decimal 120681)
   defining Unicode char U+1D76A (decimal 120682)
   defining Unicode char U+1D76B (decimal 120683)
   defining Unicode char U+1D76C (decimal 120684)
   defining Unicode char U+1D76D (decimal 120685)
   defining Unicode char U+1D76E (decimal 120686)
   defining Unicode char U+1D76F (decimal 120687)
   defining Unicode char U+1D770 (decimal 120688)
   defining Unicode char U+1D771 (decimal 120689)
   defining Unicode char U+1D772 (decimal 120690)
   defining Unicode char U+1D773 (decimal 120691)
   defining Unicode char U+1D774 (decimal 120692)
   defining Unicode char U+1D775 (decimal 120693)
   defining Unicode char U+1D776 (decimal 120694)
   defining Unicode char U+1D777 (decimal 120695)
   defining Unicode char U+1D778 (decimal 120696)
   defining Unicode char U+1D779 (decimal 120697)
   defining Unicode char U+1D77A (decimal 120698)
   defining Unicode char U+1D77B (decimal 120699)
   defining Unicode char U+1D77C (decimal 120700)
   defining Unicode char U+1D77D (decimal 120701)
   defining Unicode char U+1D77E (decimal 120702)
   defining Unicode char U+1D77F (decimal 120703)
   defining Unicode char U+1D780 (decimal 120704)
   defining Unicode char U+1D781 (decimal 120705)
   defining Unicode char U+1D782 (decimal 120706)
   defining Unicode char U+1D783 (decimal 120707)
   defining Unicode char U+1D784 (decimal 120708)
   defining Unicode char U+1D785 (decimal 120709)
   defining Unicode char U+1D786 (decimal 120710)
   defining Unicode char U+1D787 (decimal 120711)
   defining Unicode char U+1D788 (decimal 120712)
   defining Unicode char U+1D789 (decimal 120713)
   defining Unicode char U+1D78A (decimal 120714)
   defining Unicode char U+1D78B (decimal 120715)
   defining Unicode char U+1D78C (decimal 120716)
   defining Unicode char U+1D78D (decimal 120717)
   defining Unicode char U+1D78E (decimal 120718)
   defining Unicode char U+1D78F (decimal 120719)
   defining Unicode char U+1D790 (decimal 120720)
   defining Unicode char U+1D791 (decimal 120721)
   defining Unicode char U+1D792 (decimal 120722)
   defining Unicode char U+1D793 (decimal 120723)
   defining Unicode char U+1D794 (decimal 120724)
   defining Unicode char U+1D795 (decimal 120725)
   defining Unicode char U+1D796 (decimal 120726)
   defining Unicode char U+1D797 (decimal 120727)
   defining Unicode char U+1D798 (decimal 120728)
   defining Unicode char U+1D799 (decimal 120729)
   defining Unicode char U+1D79A (decimal 120730)
   defining Unicode char U+1D79B (decimal 120731)
   defining Unicode char U+1D79C (decimal 120732)
   defining Unicode char U+1D79D (decimal 120733)
   defining Unicode char U+1D79E (decimal 120734)
   defining Unicode char U+1D79F (decimal 120735)
   defining Unicode char U+1D7A0 (decimal 120736)
   defining Unicode char U+1D7A1 (decimal 120737)
   defining Unicode char U+1D7A2 (decimal 120738)
   defining Unicode char U+1D7A3 (decimal 120739)
   defining Unicode char U+1D7A4 (decimal 120740)
   defining Unicode char U+1D7A5 (decimal 120741)
   defining Unicode char U+1D7A6 (decimal 120742)
   defining Unicode char U+1D7A7 (decimal 120743)
   defining Unicode char U+1D7A8 (decimal 120744)
   defining Unicode char U+1D7A9 (decimal 120745)
   defining Unicode char U+1D7AA (decimal 120746)
   defining Unicode char U+1D7AB (decimal 120747)
   defining Unicode char U+1D7AC (decimal 120748)
   defining Unicode char U+1D7AD (decimal 120749)
   defining Unicode char U+1D7AE (decimal 120750)
   defining Unicode char U+1D7AF (decimal 120751)
   defining Unicode char U+1D7B0 (decimal 120752)
   defining Unicode char U+1D7B1 (decimal 120753)
   defining Unicode char U+1D7B2 (decimal 120754)
   defining Unicode char U+1D7B3 (decimal 120755)
   defining Unicode char U+1D7B4 (decimal 120756)
   defining Unicode char U+1D7B5 (decimal 120757)
   defining Unicode char U+1D7B6 (decimal 120758)
   defining Unicode char U+1D7B7 (decimal 120759)
   defining Unicode char U+1D7B8 (decimal 120760)
   defining Unicode char U+1D7B9 (decimal 120761)
   defining Unicode char U+1D7BA (decimal 120762)
   defining Unicode char U+1D7BB (decimal 120763)
   defining Unicode char U+1D7BC (decimal 120764)
   defining Unicode char U+1D7BD (decimal 120765)
   defining Unicode char U+1D7BE (decimal 120766)
   defining Unicode char U+1D7BF (decimal 120767)
   defining Unicode char U+1D7C0 (decimal 120768)
   defining Unicode char U+1D7C1 (decimal 120769)
   defining Unicode char U+1D7C2 (decimal 120770)
   defining Unicode char U+1D7C3 (decimal 120771)
   defining Unicode char U+1D7C4 (decimal 120772)
   defining Unicode char U+1D7C5 (decimal 120773)
   defining Unicode char U+1D7C6 (decimal 120774)
   defining Unicode char U+1D7C7 (decimal 120775)
   defining Unicode char U+1D7C8 (decimal 120776)
   defining Unicode char U+1D7C9 (decimal 120777)
   defining Unicode char U+1D7CE (decimal 120782)
   defining Unicode char U+1D7CF (decimal 120783)
   defining Unicode char U+1D7D0 (decimal 120784)
   defining Unicode char U+1D7D1 (decimal 120785)
   defining Unicode char U+1D7D2 (decimal 120786)
   defining Unicode char U+1D7D3 (decimal 120787)
   defining Unicode char U+1D7D4 (decimal 120788)
   defining Unicode char U+1D7D5 (decimal 120789)
   defining Unicode char U+1D7D6 (decimal 120790)
   defining Unicode char U+1D7D7 (decimal 120791)
   defining Unicode char U+1D7D8 (decimal 120792)
   defining Unicode char U+1D7D9 (decimal 120793)
   defining Unicode char U+1D7DA (decimal 120794)
   defining Unicode char U+1D7DB (decimal 120795)
   defining Unicode char U+1D7DC (decimal 120796)
   defining Unicode char U+1D7DD (decimal 120797)
   defining Unicode char U+1D7DE (decimal 120798)
   defining Unicode char U+1D7DF (decimal 120799)
   defining Unicode char U+1D7E0 (decimal 120800)
   defining Unicode char U+1D7E1 (decimal 120801)
   defining Unicode char U+1D7E2 (decimal 120802)
   defining Unicode char U+1D7E3 (decimal 120803)
   defining Unicode char U+1D7E4 (decimal 120804)
   defining Unicode char U+1D7E5 (decimal 120805)
   defining Unicode char U+1D7E6 (decimal 120806)
   defining Unicode char U+1D7E7 (decimal 120807)
   defining Unicode char U+1D7E8 (decimal 120808)
   defining Unicode char U+1D7E9 (decimal 120809)
   defining Unicode char U+1D7EA (decimal 120810)
   defining Unicode char U+1D7EB (decimal 120811)
   defining Unicode char U+1D7EC (decimal 120812)
   defining Unicode char U+1D7ED (decimal 120813)
   defining Unicode char U+1D7EE (decimal 120814)
   defining Unicode char U+1D7EF (decimal 120815)
   defining Unicode char U+1D7F0 (decimal 120816)
   defining Unicode char U+1D7F1 (decimal 120817)
   defining Unicode char U+1D7F2 (decimal 120818)
   defining Unicode char U+1D7F3 (decimal 120819)
   defining Unicode char U+1D7F4 (decimal 120820)
   defining Unicode char U+1D7F5 (decimal 120821)
   defining Unicode char U+1D7F6 (decimal 120822)
   defining Unicode char U+1D7F7 (decimal 120823)
   defining Unicode char U+1D7F8 (decimal 120824)
   defining Unicode char U+1D7F9 (decimal 120825)
   defining Unicode char U+1D7FA (decimal 120826)
   defining Unicode char U+1D7FB (decimal 120827)
   defining Unicode char U+1D7FC (decimal 120828)
   defining Unicode char U+1D7FD (decimal 120829)
   defining Unicode char U+1D7FE (decimal 120830)
   defining Unicode char U+1D7FF (decimal 120831)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/babel\babel.sty
Package: babel 2024/06/26 v24.7 The Babel package
\babel@savecnt=\count288
\U@D=\dimen170
\l@unhyphenated=\language79

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/babel\txtbabel.de
f)
\bbl@readstream=\read2
\bbl@dirlevel=\count289

*************************************
* Local config file bblopts.cfg used
*
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/arabi\bblopts.cfg
File: bblopts.cfg 2005/09/08 v0.1 add Arabic and Farsi to "declared" options of
 babel
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/babel-english\engli
sh.ldf
Language: english 2017/06/06 v3.3r English support from the babel system
Package babel Info: Hyphen rules for 'canadian' set to \l@english
(babel)             (\language0). Reported on input line 102.
Package babel Info: Hyphen rules for 'australian' set to \l@ukenglish
(babel)             (\language73). Reported on input line 105.
Package babel Info: Hyphen rules for 'newzealand' set to \l@ukenglish
(babel)             (\language73). Reported on input line 108.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/babel/locale/en\b
abel-english.tex
Package babel Info: Importing font and identification data for english
(babel)             from babel-en.ini. Reported on input line 11.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\tabularx.sty
Package: tabularx 2023/12/11 v2.12a `tabularx' package (DPC)
\TX@col@width=\dimen171
\TX@old@table=\dimen172
\TX@old@col=\dimen173
\TX@target=\dimen174
\TX@delta=\dimen175
\TX@cols=\count290
\TX@ftn=\toks24
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\color.st
y
Package: color 2024/01/14 v1.3d Standard LaTeX Color (DPC)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.
cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.
ltx))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amssymb.st
y
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.s
ty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2024/05/23 v2.17q AMS math features
\@mathmargin=\skip57
For additional information on amsmath, use the `?' option.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen176
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.st
y
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count291
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count292
\leftroot@=\count293
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count294
\DOTSCASE@=\count295
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box69
\strutbox@=\box70
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen177
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count296
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count297
\dotsspace@=\muskip17
\c@parentequation=\count298
\dspbrk@lvl=\count299
\tag@help=\toks25
\row@=\count300
\column@=\count301
\maxfields@=\count302
\andhelp@=\toks26
\eqnshift@=\dimen178
\alignsep@=\dimen179
\tagshift@=\dimen180
\tagwidth@=\dimen181
\totwidth@=\dimen182
\lineht@=\dimen183
\@envbody=\toks27
\multlinegap=\skip58
\multlinetaggap=\skip59
\mathdisplay@stack=\toks28
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amscls\amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks29
\thm@bodyfont=\toks30
\thm@headfont=\toks31
\thm@notefont=\toks32
\thm@headpunct=\toks33
\thm@preskip=\skip60
\thm@postskip=\skip61
\thm@headsep=\skip62
\dth@everypar=\toks34
)
\affilsep=\skip63
\@affilsep=\skip64
\c@Maxaffil=\count303
\c@authors=\count304
\c@affil=\count305
\c@authnum=\count306
\corr@cnt=\count307
\curr@corr@cnt=\count308

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/accents\accents.sty
Package: accents 2006/05/12 v1.4 Math Accent Tools
\cc@skew=\dimen184
\cc@wd=\dimen185
\cc@code=\count309
\cc@group=\count310
\cc@skewchar=\count311
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/cancel\cancel.sty
Package: cancel 2013/04/12 v2.2 Cancel math terms
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/multirow\multirow.s
ty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip65
\multirow@cntb=\count312
\multirow@dima=\skip66
\bigstrutjot=\dimen186
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/supertabular\supert
abular.sty
Package: supertabular 2024/07/20 v4.2c the supertabular environment
\c@tracingst=\count313
\ST@wd=\dimen187
\ST@rightskip=\skip67
\ST@leftskip=\skip68
\ST@parfillskip=\skip69
\ST@pageleft=\dimen188
\ST@headht=\dimen189
\ST@tailht=\dimen190
\ST@pagesofar=\dimen191
\ST@pboxht=\dimen192
\ST@rowht=\dimen193
\ST@prevht=\dimen194
\ST@toadd=\dimen195
\ST@dimen=\dimen196
\ST@pbox=\box71
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/algorithms\algorith
mic.sty
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
\c@ALC@unique=\count314
\c@ALC@line=\count315
\c@ALC@rem=\count316
\c@ALC@depth=\count317
\ALC@tlm=\skip70
\algorithmicindent=\skip71
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/algorithms\algorith
m.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating enviro
nment

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/float\float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count318
\float@exts=\toks35
\float@box=\box72
\@float@everytoks=\toks36
\@floatcapt=\box73
)
\@float@every@algorithm=\toks37
\c@algorithm=\count319
)
\c@figscheme=\count320
\c@plates=\count321
\c@listings=\count322
\c@boxes=\count323

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption3.st
y
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen197
\captionmargin=\dimen198
\caption@leftmargin=\dimen199
\caption@rightmargin=\dimen256
\caption@width=\dimen257
\caption@indent=\dimen258
\caption@parindent=\dimen259
\caption@hangindent=\dimen260
Package caption Info: Unknown document class (or package),
(caption)             standard defaults will be used.
Package caption Info: \@makecaption = \long macro:#1#2->\def \@tempa {figure}\i
fx \@captype \@tempa \if@stage@final \vskip 0.7\abovecaptionskip \else \vskip \
abovecaptionskip \goodbreak \fi \fi {\let \in@caption \relax \reset@font \small
 {\bfseries #1.} #2\par }\if@cop@home \ifonline \ifnum \csname c@\@captype \end
csname =1 \immediate \write \@auxout {\string \gdef \string \@num \@captype {}}
\hypertarget {\@captype }{}\fi \fi \fi \def \@tempa {table}\ifx \@captype \@tem
pa \vskip \abovecaptionskip \if@stage@final \else \goodbreak \fi \fi  on input 
line 1175.
)

Package caption Warning: Unknown document class (or package),
(caption)                standard defaults will be used.
See the caption package documentation for explanation.

\c@caption@flags=\count324
\c@continuedfloat=\count325
Package caption Info: float package is loaded.
Package caption Info: supertabular package is loaded.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\listings.s
ty
\lst@mode=\count326
\lst@gtempboxa=\box74
\lst@token=\toks38
\lst@length=\count327
\lst@currlwidth=\dimen261
\lst@column=\count328
\lst@pos=\count329
\lst@lostspace=\dimen262
\lst@width=\dimen263
\lst@newlines=\count330
\lst@lineno=\count331
\lst@maxwidth=\dimen264

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\lstpatch.s
ty
File: lstpatch.sty 2024/05/25 1.10b (Carsten Heinz)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\lstmisc.st
y
File: lstmisc.sty 2024/05/25 1.10b (Carsten Heinz)
\c@lstnumber=\count332
\lst@skipnumbers=\count333
\lst@framebox=\box75
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\listings.c
fg
File: listings.cfg 2024/05/25 1.10b listings configuration
))
Package: listings 2024/05/25 1.10b (Carsten Heinz)
\listingnumwidth=\dimen265

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/subfloat\subfloat.s
ty
Package: subfloat 2003/08/21 version 2.14
 Package `subfloat', Version 2.14 of 2003/08/21.

Package subfloat Warning: Numbers of floats not counted:
(subfloat)                If you use one of the counters subfloatfiguremax or
(subfloat)                subfloattablemax you will get strange error messages
(subfloat)                containing \c@subfloatfiguremax or
(subfloat)                \c@subfloattablemax:
(subfloat)                Please switch on countmax or remove the code using
(subfloat)                the counter then..

) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/natbib\natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip72
\bibsep=\skip73
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count334
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\rotating.s
ty
Package: rotating 2016/08/11 v2.16d rotated objects in LaTeX
\c@r@tfl@t=\count335
\rotFPtop=\skip74
\rotFPbot=\skip75
\rot@float@box=\box76
\rot@mess@toks=\toks39
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/lineno\lineno.sty
Package: lineno 2023/05/20 line numbers on paragraphs v5.3

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions
.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys
.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))
\linenopenalty=\count336
\output=\toks40
\linenoprevgraf=\count337
\linenumbersep=\dimen266
\linenumberwidth=\dimen267
\c@linenumber=\count338
\c@pagewiselinenumber=\count339
\c@LN@truepage=\count340
\c@internallinenumber=\count341
\c@internallinenumbers=\count342
\quotelinenumbersep=\dimen268
\bframerule=\dimen269
\bframesep=\dimen270
\bframebox=\box77
\linenoamsmath@ams@eqpen=\count343
LaTeX Info: Redefining \\ on input line 3180.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\times.sty
Package: times 2020/03/25 PSNFSS-v9.3 (SPQR) 
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\pifont.sty
Package: pifont 2020/03/25 PSNFSS-v9.3 Pi font support (SPQR) 
LaTeX Font Info:    Trying to load font information for U+pzd on input line 63.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\upzd.fd
File: upzd.fd 2001/06/04 font definitions for U/pzd.
)
LaTeX Font Info:    Trying to load font information for U+psy on input line 64.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\upsy.fd
File: upsy.fd 2001/06/04 font definitions for U/psy.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hyperref.s
ty
Package: hyperref 2024-07-10 v7.01j Hypertext links for LaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvde
finekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdfescape\pdfesca
pe.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\nameref.st
y
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/refcount\refcount.s
ty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/gettitlestring\ge
ttitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count344
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/stringenc\stringe
nc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen271
\Hy@linkcounter=\count345
\Hy@pagecounter=\count346

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2024-07-10 v7.01j Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/intcalc\intcalc.s
ty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count347

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2024-07-10 v7.01j Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Option `bookmarks' set `false' on input line 4040.
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks OFF on input line 4430.
\c@Hy@tempcnt=\count348
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen272

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bigintcalc\bigint
calc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count349
\Field@Width=\dimen273
\Fld@charsize=\dimen274
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atbegshi-ltx.s
ty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count350
\c@Item=\count351
\c@Hfootnote=\count352
)
Package hyperref Info: Driver (autodetected): hpdftex.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hpdftex.de
f
File: hpdftex.def 2024-07-10 v7.01j Hyperref driver for pdfTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atveryend-ltx.
sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count353
\c@bookmark@seq@number=\count354

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/rerunfilecheck\reru
nfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/uniquecounter\uni
quecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip76
)
LaTeX Info: Redefining \unit on input line 2681.
\c@reaction=\count355
\tabularwidth=\dimen275
Package color Info: Redefining color textcol on input line 2991.
Package color Info: Redefining color bgcol on input line 2992.
Package color Info: Redefining color barcol on input line 2993.
)
LaTeX Info: Redefining \vec on input line 3.
\figwidth=\skip77

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/cleveref\cleveref.s
ty
Package: cleveref 2018/03/27 v0.21.4 Intelligent cross-referencing
Package cleveref Info: `hyperref' support loaded on input line 2370.
LaTeX Info: Redefining \cref on input line 2370.
LaTeX Info: Redefining \Cref on input line 2370.
LaTeX Info: Redefining \crefrange on input line 2370.
LaTeX Info: Redefining \Crefrange on input line 2370.
LaTeX Info: Redefining \cpageref on input line 2370.
LaTeX Info: Redefining \Cpageref on input line 2370.
LaTeX Info: Redefining \cpagerefrange on input line 2370.
LaTeX Info: Redefining \Cpagerefrange on input line 2370.
LaTeX Info: Redefining \labelcref on input line 2370.
LaTeX Info: Redefining \labelcpageref on input line 2370.
Package cleveref Info: `amsthm' support loaded on input line 3026.
Package cleveref Info: `listings' support loaded on input line 3131.
Package cleveref Info: no abbreviation of names on input line 7852.
)
LaTeX Font Info:    Trying to load font information for T1+ptm on input line 18
.
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\t1ptm.fd
File: t1ptm.fd 2001/06/04 font definitions for T1/ptm.
)

LaTeX Warning: Unused global option(s):
    [journalabbreviation].

(TTA_acp_ITCZ_Aug_2025_EGU.aux)
\openout1 = `TTA_acp_ITCZ_Aug_2025_EGU.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/translations/dicts\
translations-basic-dictionary-english.trsl
File: translations-basic-dictionary-english.trsl (english translation file `tra
nslations-basic-dictionary')
)
Package translations Info: loading dictionary `translations-basic-dictionary' f
or `english'. on input line 18.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pd
f.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count356
\scratchdimen=\dimen276
\scratchbox=\box78
\nofMPsegments=\count357
\nofMParguments=\count358
\everyMPshowfont=\toks41
\MPscratchCnt=\count359
\MPscratchDim=\dimen277
\MPnumerator=\count360
\makeMPintoPDFobject=\count361
\everyMPtoPDFconversion=\toks42
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstop
df-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/grfext\grfext.sty
Package: grfext 2019/12/03 v1.3 Manage graphics extensions (HO)
)
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.
Package grfext Info: Graphics extension search list:
(grfext)             [.pdf,.png,.jpg,.eps]
(grfext)             \AppendGraphicsExtensions on input line 504.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/00miktex\epstopdf-s
ys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: hyperref package is loaded.
Package caption Info: listings package is loaded.
Package caption Info: rotating package is loaded.
Package caption Info: End \AtBeginDocument code.
\c@lstlisting=\count362
Package hyperref Info: Link coloring OFF on input line 18.
LaTeX Font Info:    Trying to load font information for U+msa on input line 66.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 66.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Trying to load font information for T1+phv on input line 74
.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\t1phv.fd
File: t1phv.fd 2020/03/25 scalable font definitions for T1/phv.
)
LaTeX Font Info:    Font shape `T1/phv/m/n' will be
(Font)              scaled to size 8.54997pt on input line 74.


Package natbib Warning: Citation `munnich2005' on page 1 undefined on input lin
e 82.


Package natbib Warning: Citation `kerns2018' on page 1 undefined on input line 
82.


Package natbib Warning: Citation `jin2023' on page 1 undefined on input line 82
.



[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}{C:/Users/
ToyeTunde/AppData/Local/Programs/MiKTeX/fonts/enc/dvips/base/8r.enc}]

Package natbib Warning: Citation `schneider2014' on page 2 undefined on input l
ine 84.


Package natbib Warning: Citation `kerns2018' on page 2 undefined on input line 
84.


Package natbib Warning: Citation `straub2002' on page 2 undefined on input line
 84.


Package natbib Warning: Citation `dias2009' on page 2 undefined on input line 8
4.


Package natbib Warning: Citation `zhang2001' on page 2 undefined on input line 
84.


Package natbib Warning: Citation `laderach2013' on page 2 undefined on input li
ne 84.


Package natbib Warning: Citation `laderach2013' on page 2 undefined on input li
ne 84.


Package natbib Warning: Citation `basha2015' on page 2 undefined on input line 
84.


Package natbib Warning: Citation `zhang2001' on page 2 undefined on input line 
84.


Package natbib Warning: Citation `alexander2000' on page 2 undefined on input l
ine 86.


Package natbib Warning: Citation `alexander2000a' on page 2 undefined on input 
line 86.


Package natbib Warning: Citation `alexander2008' on page 2 undefined on input l
ine 86.


Package natbib Warning: Citation `schmidt2008' on page 2 undefined on input lin
e 86.


Package natbib Warning: Citation `smith2020' on page 2 undefined on input line 
86.


Package natbib Warning: Citation `luo2021' on page 2 undefined on input line 86
.


Package natbib Warning: Citation `bain2011' on page 2 undefined on input line 8
6.


Package natbib Warning: Citation `alexander2018' on page 2 undefined on input l
ine 88.


Package natbib Warning: Citation `konopka2016' on page 2 undefined on input lin
e 88.


Package natbib Warning: Citation `diallo2019' on page 2 undefined on input line
 88.


Package natbib Warning: Citation `diallo2019' on page 2 undefined on input line
 88.


Package natbib Warning: Citation `diallo2021' on page 2 undefined on input line
 88.


Package natbib Warning: Citation `moss2016' on page 2 undefined on input line 8
8.


Package natbib Warning: Citation `alexander2018' on page 2 undefined on input l
ine 88.


Package natbib Warning: Citation `godoi2020' on page 2 undefined on input line 
88.


Package natbib Warning: Citation `wei2024' on page 2 undefined on input line 88
.


Package natbib Warning: Citation `geller2016' on page 2 undefined on input line
 88.


Package natbib Warning: Citation `liu2017' on page 2 undefined on input line 88
.


Package natbib Warning: Citation `godoi2020' on page 2 undefined on input line 
88.


Package natbib Warning: Citation `ern2014' on page 2 undefined on input line 88
.


Package natbib Warning: Citation `geller2016' on page 2 undefined on input line
 88.


Package natbib Warning: Citation `kang2020' on page 2 undefined on input line 8
8.


Package natbib Warning: Citation `holt2022' on page 2 undefined on input line 8
8.


Package natbib Warning: Citation `lee2024' on page 2 undefined on input line 88
.


Package natbib Warning: Citation `klotzbach2019' on page 2 undefined on input l
ine 88.



[2]

LaTeX Warning: Reference `Res' on page 1 undefined on input line 90.


LaTeX Warning: Reference `Dis' on page 1 undefined on input line 90.


LaTeX Warning: Reference `Con' on page 1 undefined on input line 90.

<Figures/OCC_Count_ITCZ_atmprf1.jpeg, id=25, 3387.65625pt x 2258.4375pt>
File: Figures/OCC_Count_ITCZ_atmprf1.jpeg Graphic file (type jpg)
<use Figures/OCC_Count_ITCZ_atmprf1.jpeg>
Package pdftex.def Info: Figures/OCC_Count_ITCZ_atmprf1.jpeg  used on input lin
e 101.
(pdftex.def)             Requested size: 300.0pt x 214.6334pt.

LaTeX Warning: Reference `fig:fig2' on page 1 undefined on input line 103.



[3]

Package natbib Warning: Citation `hersbach2020' on page 4 undefined on input li
ne 108.


Package natbib Warning: Citation `kalnay2018' on page 4 undefined on input line
 108.



[4 <./Figures/OCC_Count_ITCZ_atmprf1.jpeg>]

Package natbib Warning: Citation `tsuda2000' on page 5 undefined on input line 
130.


Package natbib Warning: Citation `ayorinde2023' on page 5 undefined on input li
ne 130.


Package natbib Warning: Citation `ayorinde2023' on page 5 undefined on input li
ne 156.


Package natbib Warning: Citation `torrence1998' on page 5 undefined on input li
ne 156.


Package natbib Warning: Citation `moss2016' on page 5 undefined on input line 1
56.


LaTeX Warning: Reference `fig:fig4' on page 1 undefined on input line 156.



[5]

Package natbib Warning: Citation `basha2009' on page 6 undefined on input line 
162.


Package natbib Warning: Citation `ratnam2010' on page 6 undefined on input line
 162.


Package natbib Warning: Citation `ratnam2010' on page 6 undefined on input line
 162.


Package natbib Warning: Citation `basha2015' on page 6 undefined on input line 
162.


Package natbib Warning: Citation `laderach2013' on page 6 undefined on input li
ne 164.


Package natbib Warning: Citation `wolter2011' on page 6 undefined on input line
 178.


Package natbib Warning: Citation `li2013' on page 6 undefined on input line 178
.


Package natbib Warning: Citation `ayorinde2024' on page 6 undefined on input li
ne 178.



[6]

Package natbib Warning: Citation `kutner2004' on page 7 undefined on input line
 188.


Package natbib Warning: Citation `mitchell2015' on page 7 undefined on input li
ne 188.


Package natbib Warning: Citation `wolter2011' on page 7 undefined on input line
 188.


Package natbib Warning: Citation `hoffmann2021' on page 7 undefined on input li
ne 188.


Package natbib Warning: Citation `gavrilov2002' on page 7 undefined on input li
ne 188.

<Figures/QBO_ENSO_MJO_Line_Plot.pdf, id=75, 1816.2888pt x 652.43124pt>
File: Figures/QBO_ENSO_MJO_Line_Plot.pdf Graphic file (type pdf)
<use Figures/QBO_ENSO_MJO_Line_Plot.pdf>
Package pdftex.def Info: Figures/QBO_ENSO_MJO_Line_Plot.pdf  used on input line
 193.
(pdftex.def)             Requested size: 480.0pt x 172.4156pt.


[7 <./Figures/QBO_ENSO_MJO_Line_Plot.pdf>]

LaTeX Warning: Reference `fig:fig3' on page 1 undefined on input line 204.


LaTeX Warning: Reference `fig:fig3' on page 1 undefined on input line 204.


LaTeX Warning: Reference `fig:fig3' on page 1 undefined on input line 204.


LaTeX Warning: Reference `fig:fig3' on page 1 undefined on input line 204.


LaTeX Warning: Reference `fig:fig3' on page 1 undefined on input line 204.

<Figures/COSMIC_Global_Plot_DJF_2021a.pdf, id=152, 794.97pt x 361.35pt>
File: Figures/COSMIC_Global_Plot_DJF_2021a.pdf Graphic file (type pdf)
<use Figures/COSMIC_Global_Plot_DJF_2021a.pdf>
Package pdftex.def Info: Figures/COSMIC_Global_Plot_DJF_2021a.pdf  used on inpu
t line 209.
(pdftex.def)             Requested size: 240.0pt x 69.29057pt.

!pdfTeX error: pdflatex.exe (file ./Figures/COSMIC_Global_Plot_JJA_2021a.pdf): 
reading image file failed
 ==> Fatal error occurred, no output PDF file produced!
