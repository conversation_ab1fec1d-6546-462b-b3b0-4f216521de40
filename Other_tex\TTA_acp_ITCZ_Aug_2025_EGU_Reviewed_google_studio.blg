This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: TTA_acp_ITCZ_Aug_2025_EGU_Reviewed_google_studio.aux
Reallocating 'name_of_file' (item size: 1) to 11 items.
The style file: copernicus.bst
Reallocating 'name_of_file' (item size: 1) to 13 items.
Database file #1: Ref_ITCZ.bib
I was expecting a `,' or a `}'---line 169 of file Ref_ITCZ.bib
 : 
 : @incollection{reichler2016,
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 231 of file Ref_ITCZ.bib
 : 
 : doi = {10.1175/1520-0442(1993)006<2162:ASDCOT>2.0.CO;2},
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 289 of file Ref_ITCZ.bib
 : 
 : doi={10.1175/2007JAS2518.1}
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
Repeated entry---line 1363 of file Ref_ITCZ.bib
 : @article{byrne2018
 :                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 2343 of file Ref_ITCZ.bib
 : @article{ern2014
 :                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 2591 of file Ref_ITCZ.bib
 : @article{kerns2018
 :                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 3063 of file Ref_ITCZ.bib
 : @article{fritts2003
 :                    ,
I'm skipping whatever remains of this entry
You're missing a field name---line 3185 of file Ref_ITCZ.bib
 : year = {2008}, 
 :                % Although cited as 2011, BAMS has this as 2008
I'm skipping whatever remains of this entry
You're missing a field name---line 3365 of file Ref_ITCZ.bib
 : year = {2010}, 
 :                % Note: Cited as 2009, but QJ paper is 2010
I'm skipping whatever remains of this entry
Repeated entry---line 3373 of file Ref_ITCZ.bib
 : @article{schmidt2016
 :                     ,
I'm skipping whatever remains of this entry
You've used 51 entries,
            2932 wiz_defined-function locations,
            1378 strings with 21390 characters,
and the built_in function-call counts, 63803 in all, are:
= -- 7396
> -- 2604
< -- 14
+ -- 1217
- -- 820
* -- 3992
:= -- 6131
add.period$ -- 51
call.type$ -- 51
change.case$ -- 377
chr.to.int$ -- 52
cite$ -- 153
duplicate$ -- 6340
empty$ -- 2956
format.name$ -- 986
if$ -- 12552
int.to.chr$ -- 2
int.to.str$ -- 1
missing$ -- 634
newline$ -- 162
num.names$ -- 204
pop$ -- 2702
preamble$ -- 1
purify$ -- 375
quote$ -- 0
skip$ -- 4009
stack$ -- 0
substring$ -- 2885
swap$ -- 5775
text.length$ -- 1
text.prefix$ -- 0
top$ -- 0
type$ -- 453
warning$ -- 0
while$ -- 289
width$ -- 0
write$ -- 618
(There were 10 error messages)
