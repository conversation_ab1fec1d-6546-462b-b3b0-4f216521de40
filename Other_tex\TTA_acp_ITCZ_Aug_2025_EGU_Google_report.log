This is pdfTeX, Version 3.141592653-2.6-1.40.26 (MiKTeX 24.4) (preloaded format=pdflatex 2024.12.28)  6 APR 2025 15:59
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./TTA_acp_ITCZ_Aug_2025_EGU_Google_report.tex
(TTA_acp_ITCZ_Aug_2025_EGU_Google_report.tex
LaTeX2e <2024-11-01>
L3 programming layer <2024-11-02>
(C:\Program Files\MiKTeX\tex/latex/base\article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(C:\Program Files\MiKTeX\tex/latex/base\size11.clo
File: size11.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(C:\Users\<USER>\AppData\Roaming\MiKTeX\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Program Files\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(C:\Program Files\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Program Files\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count270
\Gm@cntv=\count271
\c@Gm@tempcnt=\count272
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks18

(C:\Users\<USER>\AppData\Roaming\MiKTeX\tex/latex/geometry\geometry.cfg))
(C:\Users\<USER>\AppData\Roaming\MiKTeX\tex/latex/parskip\parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments

(C:\Users\<USER>\AppData\Roaming\MiKTeX\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)

(C:\Users\<USER>\AppData\Roaming\MiKTeX\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:\Users\<USER>\AppData\Roaming\MiKTeX\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))
(C:\Program Files\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count273
))
(C:\Users\<USER>\AppData\Roaming\MiKTeX\tex/latex/psnfss\times.sty
Package: times 2020/03/25 PSNFSS-v9.3 (SPQR) 
)
(C:\Program Files\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
(C:\Program Files\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(C:\Program Files\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(C:\Program Files\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen150
))
(C:\Program Files\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen151
)
(C:\Program Files\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count274
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count275
\leftroot@=\count276
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count277
\DOTSCASE@=\count278
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen152
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count279
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count280
\dotsspace@=\muskip18
\c@parentequation=\count281
\dspbrk@lvl=\count282
\tag@help=\toks20
\row@=\count283
\column@=\count284
\maxfields@=\count285
\andhelp@=\toks21
\eqnshift@=\dimen153
\alignsep@=\dimen154
\tagshift@=\dimen155
\tagwidth@=\dimen156
\totwidth@=\dimen157
\lineht@=\dimen158
\@envbody=\toks22
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 1
3.

(C:\Users\<USER>\AppData\Roaming\MiKTeX\tex/latex/psnfss\ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
(C:\Program Files\MiKTeX\tex/latex/l3backend\l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count286
\l__pdf_internal_box=\box54
)
(TTA_acp_ITCZ_Aug_2025_EGU_Google_report.aux)
\openout1 = `TTA_acp_ITCZ_Aug_2025_EGU_Google_report.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=59.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)



[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}{C:/Program Fi
les/MiKTeX/fonts/enc/dvips/base/8r.enc}]
LaTeX Font Info:    Trying to load font information for TS1+ptm on input line 6
4.

(C:\Users\<USER>\AppData\Roaming\MiKTeX\tex/latex/psnfss\ts1ptm.fd
File: ts1ptm.fd 2001/06/04 font definitions for TS1/ptm.
)
Overfull \hbox (4.24144pt too wide) in paragraph at lines 66--68
\OT1/ptm/b/n/10.95 Reviewer/Editor Com-ment: \OT1/ptm/m/n/10.95 Ma-jor Is-sue 2
: METHOD-OLOG-I-CAL IS-SUES AND AM-BI-GU-I-TIES \OT1/ptm/b/n/10.95 (Kelvin
 []

LaTeX Font Info:    Trying to load font information for OML+ptm on input line 6
9.
(C:\Users\<USER>\AppData\Roaming\MiKTeX\tex/latex/psnfss\omlptm.fd
File: omlptm.fd 
)
LaTeX Font Info:    Font shape `OML/ptm/m/n' in size <10.95> not available
(Font)              Font shape `OML/cmm/m/it' tried instead on input line 69.


[2]

LaTeX Warning: Citation `fritts2003' on page 3 undefined on input line 118.


LaTeX Warning: Citation `alexander2010' on page 3 undefined on input line 118.



[3]

[4]

[5]

[6]

[7]

[8] (TTA_acp_ITCZ_Aug_2025_EGU_Google_report.aux)
 ***********
LaTeX2e <2024-11-01>
L3 programming layer <2024-11-02>
 ***********


LaTeX Warning: There were undefined references.

 ) 
Here is how much of TeX's memory you used:
 2659 strings out of 473682
 41035 string characters out of 5729114
 445665 words of memory out of 5000000
 25739 multiletter control sequences out of 15000+600000
 570351 words of font info for 60 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 57i,6n,65p,867b,156s stack positions out of 10000i,1000n,20000p,200000b,200000s
<C:/Program Files/MiKTeX/fonts/type1/public/amsfonts/cm/cmmi10.pfb><C:/Progra
m Files/MiKTeX/fonts/type1/public/amsfonts/cm/cmmi8.pfb><C:/Program Files/MiKTe
X/fonts/type1/public/amsfonts/cm/cmr10.pfb><C:/Program Files/MiKTeX/fonts/type1
/public/amsfonts/cm/cmr8.pfb><C:/Program Files/MiKTeX/fonts/type1/public/amsfon
ts/cm/cmsy10.pfb><C:/Program Files/MiKTeX/fonts/type1/urw/times/utmb8a.pfb><C:/
Program Files/MiKTeX/fonts/type1/urw/times/utmr8a.pfb>
Output written on TTA_acp_ITCZ_Aug_2025_EGU_Google_report.pdf (8 pages, 110472 
bytes).
PDF statistics:
 65 PDF objects out of 1000 (max. 8388607)
 0 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

