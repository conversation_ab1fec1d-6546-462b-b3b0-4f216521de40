This is pdfTeX, Version 3.141592653-2.6-1.40.26 (MiKTeX 24.4) (preloaded format=pdflatex 2024.8.5)  22 APR 2025 14:34
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./TTA_acp_ITCZ_RC1_Google_Studio_2.tex
(TTA_acp_ITCZ_RC1_Google_Studio_2.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2024/02/08 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size10.clo
File: size10.clo 2024/02/08 v1.4n Standard LaTeX file (size option)
)
\c@part=\count194
\c@section=\count195
\c@subsection=\count196
\c@subsubsection=\count197
\c@paragraph=\count198
\c@subparagraph=\count199
\c@figure=\count266
\c@table=\count267
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.s
ty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count268
\Gm@cntv=\count269
\c@Gm@tempcnt=\count270
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks18

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.c
fg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/parskip\parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions
.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.s
ty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys
.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.s
ty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count271
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks19
\inpenc@posthook=\toks20
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/lipsum\lipsum.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3packages/l3keys2e
\l3keys2e.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3kernel\expl3.sty
Package: expl3 2024-05-27 L3 programming layer (loader) 

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend
-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count272
\l__pdf_internal_box=\box52
))
Package: l3keys2e 2024-05-08 LaTeX2e option processing using LaTeX3 keys
)
Package: lipsum 2021-09-20 v2.7 150 paragraphs of Lorem Ipsum dummy text
\g__lipsum_par_int=\count273
\l__lipsum_a_int=\count274
\l__lipsum_b_int=\count275

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/lipsum\lipsum.ltd.t
ex)) (TTA_acp_ITCZ_RC1_Google_Studio_2.aux)
\openout1 = `TTA_acp_ITCZ_RC1_Google_Studio_2.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 10.
LaTeX Font Info:    ... okay on input line 10.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 10.
LaTeX Font Info:    ... okay on input line 10.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 10.
LaTeX Font Info:    ... okay on input line 10.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 10.
LaTeX Font Info:    ... okay on input line 10.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 10.
LaTeX Font Info:    ... okay on input line 10.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 10.
LaTeX Font Info:    ... okay on input line 10.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 10.
LaTeX Font Info:    ... okay on input line 10.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 452.9679pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 700.50687pt, 72.26999pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=452.9679pt
* \textheight=700.50687pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


LaTeX Warning: Reference `eqn:eqn6_revised' on page 1 undefined on input line 2
0.

LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <7> on input line 22.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <5> on input line 22.


[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}]
Overfull \hbox (9.90761pt too wide) in paragraph at lines 45--46
 \OT1/cmr/bx/n/10 Com-ment 2: METHOD-OLOG-I-CAL IS-SUES AND AM-BI-GU-I-TIES[] \
OT1/cmr/m/it/10 Re-viewer Com-ment (Kelvin
 []

! Undefined control sequence.
l.49 ... our CWT filtering method ($\sim$ 2-15{\km
                                                  }).
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

! Undefined control sequence.
l.63 ... banding seen in Figure 4 (e.g., near \ang
                                                  {25}N/S), while we believe...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Warning: Reference `eqn:eqn_gauss' on page 2 undefined on input line 69.



[2]
! Undefined control sequence.
l.70 ... the latitude range used for the fit (\ang
                                                  {30}S to \ang{30}N).
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

! Undefined control sequence.
l.70 ... range used for the fit (\ang{30}S to \ang
                                                  {30}N).
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Warning: Reference `eqn:eqn6_revised' on page 3 undefined on input line 7
7.



[3]

LaTeX Warning: Reference `eqn:eqn1' on page 4 undefined on input line 139.


LaTeX Warning: Reference `eqn:eqnNBV' on page 4 undefined on input line 140.



[4]
! Undefined control sequence.
l.163 ...: "The ITCZ typically resides around \ang
                                                  {5}-\ang{10}S during DJF a...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

! Undefined control sequence.
l.163 ...TCZ typically resides around \ang{5}-\ang
                                                  {10}S during DJF and shift...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

! Undefined control sequence.
l.163 ...}-\ang{10}S during DJF and shifts to \ang
                                                  {5}-\ang{15}N during JJA, ...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

! Undefined control sequence.
l.163 ...0}S during DJF and shifts to \ang{5}-\ang
                                                  {15}N during JJA, resultin...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

! Undefined control sequence.
l.163 ...resulting in a seasonal migration of \ang
                                                  {10}-\ang{20}... While the...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

! Undefined control sequence.
l.163 ... in a seasonal migration of \ang{10}-\ang
                                                  {20}... While the mean sea...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.



[5])
Runaway argument?
{Formulated a Clear Scientific Question and Hypothesis: The Introduct\ETC.
! File ended while scanning use of \textbf .
<inserted text> 
                \par 
<*> ./TTA_acp_ITCZ_RC1_Google_Studio_2.tex
                                          
I suspect you have forgotten a `}', causing me
to read past where you wanted me to stop.
I'll try to recover; but if the error is serious,
you'd better type `E' or `X' now and fix your file.

! Emergency stop.
<*> ./TTA_acp_ITCZ_RC1_Google_Studio_2.tex
                                          
*** (job aborted, no legal \end found)

 
Here is how much of TeX's memory you used:
 2194 strings out of 473904
 40463 string characters out of 5724713
 1938908 words of memory out of 5000000
 25088 multiletter control sequences out of 15000+600000
 560268 words of font info for 43 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 79i,5n,93p,900b,194s stack positions out of 10000i,1000n,20000p,200000b,200000s
!  ==> Fatal error occurred, no output PDF file produced!
