This is pdfTeX, Version 3.141592653-2.6-1.40.26 (MiKTeX 24.4) (preloaded format=pdflatex 2024.8.5)  7 JUL 2025 15:58
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./Reviewer_Response_Template.tex
(Reviewer_Response_Template.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2024/02/08 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size11.clo
File: size11.clo 2024/02/08 v1.4n Standard LaTeX file (size option)
)
\c@part=\count194
\c@section=\count195
\c@subsection=\count196
\c@subsubsection=\count197
\c@paragraph=\count198
\c@subparagraph=\count199
\c@figure=\count266
\c@table=\count267
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.s
ty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks19
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count268
\Gm@cntv=\count269
\c@Gm@tempcnt=\count270
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks20

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.c
fg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.
cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex
.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.
ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/enumitem\enumitem.s
ty
Package: enumitem 2025/01/19 v3.10 Customized lists
\labelindent=\skip51
\enit@outerparindent=\dimen150
\enit@toks=\toks21
\enit@inbox=\box52
\enit@count@id=\count271
\enitdp@description=\count272
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/titlesec\titlesec.s
ty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box53
\beforetitleunit=\skip52
\aftertitleunit=\skip53
\ttl@plus=\dimen151
\ttl@minus=\dimen152
\ttl@toksa=\toks22
\titlewidth=\dimen153
\titlewidthlast=\dimen154
\titlewidthfirst=\dimen155
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fancyhdr\fancyhdr.s
ty
Package: fancyhdr 2025/01/07 v5.1.1 Extensive control of page headers and foote
rs
\f@nch@headwidth=\skip54
\f@nch@offset@elh=\skip55
\f@nch@offset@erh=\skip56
\f@nch@offset@olh=\skip57
\f@nch@offset@orh=\skip58
\f@nch@offset@elf=\skip59
\f@nch@offset@erf=\skip60
\f@nch@offset@olf=\skip61
\f@nch@offset@orf=\skip62
\f@nch@height=\skip63
\f@nch@footalignment=\skip64
\f@nch@widthL=\skip65
\f@nch@widthC=\skip66
\f@nch@widthR=\skip67
\@temptokenb=\toks23
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hyperref.s
ty
Package: hyperref 2024-07-10 v7.01j Hypertext links for LaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys
.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvde
finekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdfescape\pdfesca
pe.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.s
ty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftex
cmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infware
rr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\nameref.st
y
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/refcount\refcount.s
ty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/gettitlestring\ge
ttitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions
.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count273
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.s
ty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count274
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/stringenc\stringe
nc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen156
\Hy@linkcounter=\count275
\Hy@pagecounter=\count276

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2024-07-10 v7.01j Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/intcalc\intcalc.s
ty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count277

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2024-07-10 v7.01j Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count278
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen157

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bigintcalc\bigint
calc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count279
\Field@Width=\dimen158
\Fld@charsize=\dimen159
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atbegshi-ltx.s
ty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count280
\c@Item=\count281
\c@Hfootnote=\count282
)
Package hyperref Info: Driver (autodetected): hpdftex.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hpdftex.de
f
File: hpdftex.def 2024-07-10 v7.01j Hyperref driver for pdfTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atveryend-ltx.
sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count283
\c@bookmark@seq@number=\count284

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/rerunfilecheck\reru
nfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/uniquecounter\uni
quecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip68
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2024/05/23 v2.17q AMS math features
\@mathmargin=\skip69
For additional information on amsmath, use the `?' option.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks24
\ex@=\dimen160
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen161
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.st
y
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count285
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count286
\leftroot@=\count287
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count288
\DOTSCASE@=\count289
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box54
\strutbox@=\box55
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen162
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count290
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count291
\dotsspace@=\muskip18
\c@parentequation=\count292
\dspbrk@lvl=\count293
\tag@help=\toks25
\row@=\count294
\column@=\count295
\maxfields@=\count296
\andhelp@=\toks26
\eqnshift@=\dimen163
\alignsep@=\dimen164
\tagshift@=\dimen165
\tagwidth@=\dimen166
\totwidth@=\dimen167
\lineht@=\dimen168
\@envbody=\toks27
\multlinegap=\skip70
\multlinetaggap=\skip71
\mathdisplay@stack=\toks28
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/siunitx\siunitx.sty
Package: siunitx 2024-06-24 v3.3.19 A comprehensive (SI) units package
\l__siunitx_number_uncert_offset_int=\count297
\l__siunitx_number_exponent_fixed_int=\count298
\l__siunitx_number_min_decimal_int=\count299
\l__siunitx_number_min_integer_int=\count300
\l__siunitx_number_round_precision_int=\count301
\l__siunitx_number_lower_threshold_int=\count302
\l__siunitx_number_upper_threshold_int=\count303
\l__siunitx_number_group_first_int=\count304
\l__siunitx_number_group_size_int=\count305
\l__siunitx_number_group_minimum_int=\count306
\l__siunitx_angle_tmp_dim=\dimen169
\l__siunitx_angle_marker_box=\box56
\l__siunitx_angle_unit_box=\box57
\l__siunitx_compound_count_int=\count307

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/translations\transl
ations.sty
Package: translations 2022/02/05 v1.12 internationalization of LaTeX2e packages
 (CN)
)
\l__siunitx_table_tmp_box=\box58
\l__siunitx_table_tmp_dim=\dimen170
\l__siunitx_table_column_width_dim=\dimen171
\l__siunitx_table_integer_box=\box59
\l__siunitx_table_decimal_box=\box60
\l__siunitx_table_uncert_box=\box61
\l__siunitx_table_before_box=\box62
\l__siunitx_table_after_box=\box63
\l__siunitx_table_before_dim=\dimen172
\l__siunitx_table_carry_dim=\dimen173
\l__siunitx_unit_tmp_int=\count308
\l__siunitx_unit_position_int=\count309
\l__siunitx_unit_total_int=\count310

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\array.sty
Package: array 2024/06/14 v2.6d Tabular extension package (FMi)
\col@sep=\dimen174
\ar@mcellbox=\box64
\extrarowheight=\dimen175
\NC@list=\toks29
\extratabsurround=\skip72
\backup@length=\skip73
\ar@cellbox=\box65
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/gensymb\gensymb.sty
Package: gensymb 2022/10/17 v1.0.2 (KJH)
)
Package translations Info: No language package found. I am going to use `englis
h' as default language. on input line 51.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend
-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count311
\l__pdf_internal_box=\box66
) (Reviewer_Response_Template.aux)
\openout1 = `Reviewer_Response_Template.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 51.
LaTeX Font Info:    ... okay on input line 51.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 51.
LaTeX Font Info:    ... okay on input line 51.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 51.
LaTeX Font Info:    ... okay on input line 51.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 51.
LaTeX Font Info:    ... okay on input line 51.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 51.
LaTeX Font Info:    ... okay on input line 51.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 51.
LaTeX Font Info:    ... okay on input line 51.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 51.
LaTeX Font Info:    ... okay on input line 51.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 51.
LaTeX Font Info:    ... okay on input line 51.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 51.
LaTeX Font Info:    ... okay on input line 51.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=702.78308pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=59.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pd
f.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count312
\scratchdimen=\dimen176
\scratchbox=\box67
\nofMPsegments=\count313
\nofMParguments=\count314
\everyMPshowfont=\toks30
\MPscratchCnt=\count315
\MPscratchDim=\dimen177
\MPnumerator=\count316
\makeMPintoPDFobject=\count317
\everyMPtoPDFconversion=\toks31
)
Package hyperref Info: Link coloring OFF on input line 51.
 (Reviewer_Response_Template.out) (Reviewer_Response_Template.out)
\@outlinefile=\write3
\openout3 = `Reviewer_Response_Template.out'.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/translations/dicts\
translations-basic-dictionary-english.trsl
File: translations-basic-dictionary-english.trsl (english translation file `tra
nslations-basic-dictionary')
)
Package translations Info: loading dictionary `translations-basic-dictionary' f
or `english'. on input line 51.
LaTeX Info: Redefining \celsius on input line 51.
Package gensymb Info: Faking symbols for \degree and \celsius on input line 51.



Package gensymb Warning: Not defining \perthousand.

LaTeX Info: Redefining \ohm on input line 51.
Package gensymb Info: Using \Omega for \ohm on input line 51.

Package gensymb Warning: Not defining \micro.



[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[2]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[3]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[4] (Reviewer_Response_Template.aux)
 ***********
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
 ***********
Package rerunfilecheck Info: File `Reviewer_Response_Template.out' has not chan
ged.
(rerunfilecheck)             Checksum: CC9160A2472BB78E1E61F28383A2FC62;4197.
 ) 
Here is how much of TeX's memory you used:
 14786 strings out of 473904
 288549 string characters out of 5724713
 1938908 words of memory out of 5000000
 37500 multiletter control sequences out of 15000+600000
 568799 words of font info for 60 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 75i,11n,79p,436b,902s stack positions out of 10000i,1000n,20000p,200000b,200000s
 <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi600\
ecbx1095.pk> <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/
ec/dpi600\ecbx1200.pk> <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour
/jknappen/ec/dpi600\ecrm1095.pk> <C:\Users\<USER>\AppData\Local\MiKTeX\fonts
/pk/ljfour/jknappen/ec/dpi600\ecbx1440.pk> <C:\Users\<USER>\AppData\Local\Mi
KTeX\fonts/pk/ljfour/jknappen/ec/dpi600\ecrm1200.pk> <C:\Users\<USER>\AppDat
a\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi600\ecbx1728.pk><C:/Users/<USER>
de/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmmi10.pfb><C:/
Users/ToyeTunde/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cm
r10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/am
sfonts/cm/cmr8.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type
1/public/amsfonts/cm/cmsy8.pfb>
Output written on Reviewer_Response_Template.pdf (4 pages, 134651 bytes).
PDF statistics:
 376 PDF objects out of 1000 (max. 8388607)
 27 named destinations out of 1000 (max. 500000)
 177 words of extra memory for PDF output out of 10000 (max. 10000000)

