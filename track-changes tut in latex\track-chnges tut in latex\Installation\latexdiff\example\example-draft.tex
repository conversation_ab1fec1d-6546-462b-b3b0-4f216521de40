\documentclass[12pt,a4paper]{article}

\setlength{\topmargin}{-0.2in}
\setlength{\textheight}{9.5in}
\setlength{\oddsidemargin}{0.0in}

\setlength{\textwidth}{6.5in}

\title{latexdiff Example - Draft version}
\author{<PERSON>mann}

\begin{document}
\maketitle

\section*{Introduction}

This is an extremely simple document that showcases some of latexdiff features.
Type
\begin{verbatim}
latexdiff -t UNDERLINE example-draft.tex example-rev.tex > example-diff.tex
\end{verbatim}
to create the difference file.  You can inspect this file directly. Then run either 
\begin{verbatim}
pdflatex example-diff.tex
xpdf example-diff.pdf
\end{verbatim}
or
\begin{verbatim}
latex example-diff.tex
dvips -o example-diff.ps example-diff.dvi
gv example-diff.ps
\end{verbatim}
to display the markup.

\section*{Another section title}

A paragraph with a line only in the draft document.  More things could be said 
were it not for the constraints of time and space.

More things could be said were it not for the constraints of time and space.

And here is a tipo. 

Here is a table:

\begin{tabular}{ll}
Name & Description \\
\hline
Gandalf & Grey \\
Saruman & White
\end{tabular}

And sometimes a whole paragraph gets completely rewritten. In this
case latexdiff marks up the whole paragraph even if some words in it
are identical.
No change, no markup!
\end{document}


