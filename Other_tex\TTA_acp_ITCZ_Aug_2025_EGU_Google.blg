This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: TTA_acp_ITCZ_Aug_2025_EGU_Google.aux
Reallocating 'name_of_file' (item size: 1) to 11 items.
The style file: copernicus.bst
Reallocating 'name_of_file' (item size: 1) to 13 items.
Database file #1: Ref_ITCZ.bib
I was expecting a `,' or a `}'---line 169 of file Ref_ITCZ.bib
 : 
 : @incollection{reichler2016,
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 231 of file Ref_ITCZ.bib
 : 
 : doi = {10.1175/1520-0442(1993)006<2162:ASDCOT>2.0.CO;2},
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 289 of file Ref_ITCZ.bib
 : 
 : doi={10.1175/2007JAS2518.1}
(<PERSON>rror may have been on previous line)
I'm skipping whatever remains of this entry
Repeated entry---line 2031 of file Ref_ITCZ.bib
 : @article{wei2024
 :                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 2343 of file Ref_ITCZ.bib
 : @article{ern2014
 :                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 2591 of file Ref_ITCZ.bib
 : @article{kerns2018
 :                   ,
I'm skipping whatever remains of this entry
Warning--I didn't find a database entry for "baldwin2001"
Warning--I didn't find a database entry for "kursinski1997"
Warning--I didn't find a database entry for "ho2020"
Warning--I didn't find a database entry for "kalnay1996"
Warning--I didn't find a database entry for "liebmann1996"
Warning--I didn't find a database entry for "wheeler2004"
Warning--I didn't find a database entry for "smith1953"
Warning--empty journal in hoffmann2021
You've used 36 entries,
            2932 wiz_defined-function locations,
            1288 strings with 17283 characters,
and the built_in function-call counts, 39163 in all, are:
= -- 4525
> -- 1444
< -- 13
+ -- 689
- -- 448
* -- 2458
:= -- 3791
add.period$ -- 36
call.type$ -- 36
change.case$ -- 222
chr.to.int$ -- 37
cite$ -- 109
duplicate$ -- 3955
empty$ -- 1936
format.name$ -- 569
if$ -- 7719
int.to.chr$ -- 1
int.to.str$ -- 1
missing$ -- 397
newline$ -- 117
num.names$ -- 144
pop$ -- 1656
preamble$ -- 1
purify$ -- 221
quote$ -- 0
skip$ -- 2404
stack$ -- 0
substring$ -- 1769
swap$ -- 3510
text.length$ -- 2
text.prefix$ -- 0
top$ -- 0
type$ -- 321
warning$ -- 1
while$ -- 193
width$ -- 0
write$ -- 438
(There were 6 error messages)
